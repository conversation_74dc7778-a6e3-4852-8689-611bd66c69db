package services

import (
	"adc-account-backend/internal/config"

	"gorm.io/gorm"
)

// Container holds all service dependencies
type Container struct {
	DB     *gorm.DB
	Config *config.Config

	// Authentication services
	AuthService *AuthService
	UserService *UserService

	// Organization services
	OrganizationService *OrganizationService
	BranchService       *BranchService

	// Financial services
	InvoiceService         *InvoiceService
	BillService            *BillService
	ExpenseService         *ExpenseService
	ExpenseCategoryService *ExpenseCategoryService
	CustomerService        *CustomerService
	VendorService          *VendorService

	// Accounting services
	AccountService      *AccountService
	JournalEntryService *JournalEntryService
	TaxRateService      *TaxRateService

	// Banking services
	BankAccountService        *BankAccountService
	BankTransactionService    *BankTransactionService
	BankReconciliationService *BankReconciliationService

	// Asset services
	AssetService *AssetService

	// Inventory services
	InventoryService *InventoryService

	// Payroll services
	PayrollService *PayrollService

	// Credit and collection services
	CreditNoteService         *CreditNoteService
	CustomerCreditService     *CustomerCreditService
	PaymentReminderService    *PaymentReminderService
	CustomerStatementService  *CustomerStatementService
	CollectionCaseService     *CollectionCaseService
	CollectionActivityService *CollectionActivityService
	CollectionTemplateService *CollectionTemplateService

	// Template services
	InvoiceTemplateService  *InvoiceTemplateService
	RecurringInvoiceService *RecurringInvoiceService
	BudgetTemplateService   *BudgetTemplateService
	EmailTemplateService    *EmailTemplateService

	// Budget and cash flow services
	BudgetService                *BudgetService
	BudgetIntegrationService     *BudgetIntegrationService
	CashFlowCategoryService      *CashFlowCategoryService
	CashFlowItemService          *CashFlowItemService
	CashFlowForecastService      *CashFlowForecastService
	RecurringCashFlowItemService *RecurringCashFlowItemService

	// Email services
	EmailLogService *EmailLogService

	// Order services
	SalesOrderService    *SalesOrderService
	PurchaseOrderService *PurchaseOrderService

	// Tax services
	TaxReportService *TaxReportService

	// Customer Portal services
	PDFService     *PDFService
	EmailService   *EmailService
	PaymentService *PaymentService

	// Subscription services
	SubscriptionService *SubscriptionService
	UsageRecordService  *UsageRecordService

	// API services
	ApiKeyService *ApiKeyService

	// Audit services
	AuditLogService *AuditLogService

	// Dashboard services
	DashboardService *DashboardService
}

// NewContainer creates a new service container with all dependencies
func NewContainer(db *gorm.DB, cfg *config.Config) *Container {
	container := &Container{
		DB:     db,
		Config: cfg,
	}

	// Initialize authentication services
	container.AuthService = NewAuthService(db, cfg)
	container.UserService = NewUserService(db)

	// Initialize organization services
	container.OrganizationService = NewOrganizationService(db)
	container.BranchService = NewBranchService(db)

	// Initialize financial services
	container.InvoiceService = NewInvoiceService(db)
	container.BillService = NewBillService(db)
	container.ExpenseService = NewExpenseService(db)
	container.ExpenseCategoryService = NewExpenseCategoryService(db)
	container.CustomerService = NewCustomerService(db)
	container.VendorService = NewVendorService(db)

	// Initialize accounting services
	container.AccountService = NewAccountService(db)
	container.JournalEntryService = NewJournalEntryService(db)
	container.TaxRateService = NewTaxRateService(db)

	// Initialize banking services
	container.BankAccountService = NewBankAccountService(db)
	container.BankTransactionService = NewBankTransactionService(db)
	container.BankReconciliationService = NewBankReconciliationService(db)

	// Initialize asset services
	container.AssetService = NewAssetService(db)

	// Initialize inventory services
	container.InventoryService = NewInventoryService(db)

	// Initialize payroll services
	container.PayrollService = NewPayrollService(db)

	// Initialize credit and collection services
	container.CreditNoteService = NewCreditNoteService(db)
	container.CustomerCreditService = NewCustomerCreditService(db)
	container.PaymentReminderService = NewPaymentReminderService(db)
	container.CustomerStatementService = NewCustomerStatementService(db)
	container.CollectionCaseService = NewCollectionCaseService(db)
	container.CollectionActivityService = NewCollectionActivityService(db)
	container.CollectionTemplateService = NewCollectionTemplateService(db)

	// Initialize template services
	container.InvoiceTemplateService = NewInvoiceTemplateService(db)
	container.RecurringInvoiceService = NewRecurringInvoiceService(db, container.InvoiceService)
	container.BudgetTemplateService = NewBudgetTemplateService(db)
	container.EmailTemplateService = NewEmailTemplateService(db)

	// Initialize budget and cash flow services
	container.BudgetService = NewBudgetService(db)
	container.BudgetIntegrationService = NewBudgetIntegrationService(db)
	container.CashFlowCategoryService = NewCashFlowCategoryService(db)
	container.CashFlowItemService = NewCashFlowItemService(db)
	container.CashFlowForecastService = NewCashFlowForecastService(db)
	container.RecurringCashFlowItemService = NewRecurringCashFlowItemService(db)

	// Initialize email services
	container.EmailLogService = NewEmailLogService(db)
	container.EmailService = NewEmailService(db)

	// Initialize customer portal services
	container.PDFService = NewPDFService(db)
	container.PaymentService = NewPaymentService(db, container.EmailService)

	// Initialize order services
	container.SalesOrderService = NewSalesOrderService(db)
	container.PurchaseOrderService = NewPurchaseOrderService(db)

	// Initialize tax services
	container.TaxReportService = NewTaxReportService(db)

	// Initialize subscription services
	container.SubscriptionService = NewSubscriptionService(db)
	container.UsageRecordService = NewUsageRecordService(db)

	// Initialize API services
	container.ApiKeyService = NewApiKeyService(db)

	// Initialize audit services
	container.AuditLogService = NewAuditLogService(db)

	// Initialize dashboard services
	container.DashboardService = NewDashboardService(db)

	return container
}

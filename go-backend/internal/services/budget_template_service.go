package services

import (
	"bytes"
	"encoding/csv"
	"fmt"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// BudgetTemplateService handles budget template business logic
type BudgetTemplateService struct {
	db *gorm.DB
}

// NewBudgetTemplateService creates a new BudgetTemplateService
func NewBudgetTemplateService(db *gorm.DB) *BudgetTemplateService {
	return &BudgetTemplateService{db: db}
}

// GetAllBudgetTemplates retrieves all budget templates with pagination
func (s *BudgetTemplateService) GetAllBudgetTemplates(page, limit int, search string) ([]models.BudgetTemplate, int64, error) {
	var templates []models.BudgetTemplate
	var total int64

	query := s.db.Model(&models.BudgetTemplate{})

	// Apply search filter
	if search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget templates: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Items").Preload("Items.Account").
		Offset(offset).Limit(limit).Order("created_at DESC").Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget templates: %w", err)
	}

	return templates, total, nil
}

// GetBudgetTemplateByID retrieves a budget template by ID
func (s *BudgetTemplateService) GetBudgetTemplateByID(id string) (*models.BudgetTemplate, error) {
	var template models.BudgetTemplate
	if err := s.db.Preload("Branch").Preload("Items").Preload("Items.Account").
		Where("id = ?", id).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("budget template not found")
		}
		return nil, fmt.Errorf("failed to fetch budget template: %w", err)
	}
	return &template, nil
}

// GetBudgetTemplatesByBranch retrieves budget templates for a specific branch
func (s *BudgetTemplateService) GetBudgetTemplatesByBranch(branchID string, page, limit int, search string, isActive *bool) ([]models.BudgetTemplate, int64, error) {
	var templates []models.BudgetTemplate
	var total int64

	query := s.db.Model(&models.BudgetTemplate{}).Where("branch_id = ?", branchID)

	// Apply search filter
	if search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply active filter
	if isActive != nil {
		query = query.Where("is_active = ?", *isActive)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget templates: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Items").Preload("Items.Account").
		Offset(offset).Limit(limit).Order("created_at DESC").Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget templates: %w", err)
	}

	return templates, total, nil
}

// GetBudgetTemplatesByOrganization retrieves budget templates for all branches in an organization
func (s *BudgetTemplateService) GetBudgetTemplatesByOrganization(organizationID string, page, limit int, search string, isActive *bool) ([]models.BudgetTemplate, int64, error) {
	var templates []models.BudgetTemplate
	var total int64

	// Join with branches to filter by organization
	query := s.db.Model(&models.BudgetTemplate{}).
		Joins("JOIN branches ON budget_templates.branch_id = branches.id").
		Where("branches.organization_id = ?", organizationID)

	// Apply search filter
	if search != "" {
		query = query.Where("budget_templates.name ILIKE ? OR budget_templates.description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply active filter
	if isActive != nil {
		query = query.Where("budget_templates.is_active = ?", *isActive)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget templates: %w", err)
	}

	// Apply pagination and get results with relationships
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Items").Preload("Items.Account").
		Offset(offset).Limit(limit).Order("budget_templates.created_at DESC").Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget templates: %w", err)
	}

	return templates, total, nil
}

// CreateBudgetTemplate creates a new budget template
func (s *BudgetTemplateService) CreateBudgetTemplate(template *models.BudgetTemplate) error {
	// Validate the budget template
	if err := s.ValidateBudgetTemplate(template); err != nil {
		return err
	}

	// Set default values
	if template.IsActive == false && len(template.Items) == 0 {
		template.IsActive = true
	}

	// Create the budget template with items in a transaction
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Create the budget template
		if err := tx.Create(template).Error; err != nil {
			return fmt.Errorf("failed to create budget template: %w", err)
		}

		// Update item foreign keys
		for i := range template.Items {
			template.Items[i].TemplateID = template.ID
		}

		// Create items if provided
		if len(template.Items) > 0 {
			if err := tx.Create(&template.Items).Error; err != nil {
				return fmt.Errorf("failed to create budget template items: %w", err)
			}
		}

		return nil
	})
}

// UpdateBudgetTemplate updates an existing budget template
func (s *BudgetTemplateService) UpdateBudgetTemplate(id string, updates *models.BudgetTemplate) (*models.BudgetTemplate, error) {
	// Check if budget template exists
	var existingTemplate models.BudgetTemplate
	if err := s.db.Where("id = ?", id).First(&existingTemplate).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("budget template not found")
		}
		return nil, fmt.Errorf("failed to fetch budget template: %w", err)
	}

	// Validate updates
	if err := s.ValidateBudgetTemplateUpdates(updates); err != nil {
		return nil, err
	}

	// Update the budget template in a transaction
	return s.updateBudgetTemplateTransaction(id, updates)
}

// DeleteBudgetTemplate deletes a budget template
func (s *BudgetTemplateService) DeleteBudgetTemplate(id string) error {
	// Check if budget template exists
	var template models.BudgetTemplate
	if err := s.db.Where("id = ?", id).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("budget template not found")
		}
		return fmt.Errorf("failed to fetch budget template: %w", err)
	}

	// Delete in transaction
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete related items first
		if err := tx.Where("template_id = ?", id).Delete(&models.BudgetTemplateItem{}).Error; err != nil {
			return fmt.Errorf("failed to delete budget template items: %w", err)
		}

		// Delete the budget template
		if err := tx.Delete(&template).Error; err != nil {
			return fmt.Errorf("failed to delete budget template: %w", err)
		}

		return nil
	})
}

// GetActiveBudgetTemplates gets active budget templates for a branch
func (s *BudgetTemplateService) GetActiveBudgetTemplates(branchID string) ([]models.BudgetTemplate, error) {
	var templates []models.BudgetTemplate

	if err := s.db.Preload("Branch").Preload("Items").Preload("Items.Account").
		Where("branch_id = ? AND is_active = ?", branchID, true).
		Order("name ASC").Find(&templates).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch active budget templates: %w", err)
	}

	return templates, nil
}

// ApplyBudgetTemplate applies a budget template to create budget items for a specific year
func (s *BudgetTemplateService) ApplyBudgetTemplate(templateID string, year int) ([]models.BudgetItem, error) {
	// Get the template with items
	template, err := s.GetBudgetTemplateByID(templateID)
	if err != nil {
		return nil, err
	}

	if !template.IsActive {
		return nil, fmt.Errorf("budget template is not active")
	}

	var budgetItems []models.BudgetItem

	// Create budget items for each month of the year
	err = s.db.Transaction(func(tx *gorm.DB) error {
		for month := 1; month <= 12; month++ {
			for _, item := range template.Items {
				budgetItem := models.BudgetItem{
					BranchID:     template.BranchID,
					AccountID:    item.AccountID,
					Year:         year,
					Month:        month,
					Amount:       item.Amount,
					ActualAmount: decimal.Zero,
					Notes:        item.Notes,
				}

				// Check if budget item already exists
				var existingItem models.BudgetItem
				err := tx.Where("branch_id = ? AND account_id = ? AND year = ? AND month = ?",
					budgetItem.BranchID, budgetItem.AccountID, budgetItem.Year, budgetItem.Month).
					First(&existingItem).Error

				if err == gorm.ErrRecordNotFound {
					// Create new budget item
					if err := tx.Create(&budgetItem).Error; err != nil {
						return fmt.Errorf("failed to create budget item: %w", err)
					}
					budgetItems = append(budgetItems, budgetItem)
				} else if err != nil {
					return fmt.Errorf("failed to check existing budget item: %w", err)
				}
				// If item exists, skip it
			}
		}

		return nil
	})

	return budgetItems, err
}

// GetBudgetTemplateItems gets items for a specific template
func (s *BudgetTemplateService) GetBudgetTemplateItems(templateID string) ([]models.BudgetTemplateItem, error) {
	var items []models.BudgetTemplateItem

	if err := s.db.Preload("Template").Preload("Account").
		Where("template_id = ?", templateID).
		Order("account_id ASC").Find(&items).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch budget template items: %w", err)
	}

	return items, nil
}

// CreateBudgetTemplateItem creates a new item for a template
func (s *BudgetTemplateService) CreateBudgetTemplateItem(item *models.BudgetTemplateItem) error {
	// Validate the item
	if err := s.ValidateBudgetTemplateItem(item); err != nil {
		return err
	}

	// Check if item already exists for this template and account
	var existingItem models.BudgetTemplateItem
	err := s.db.Where("template_id = ? AND account_id = ?", item.TemplateID, item.AccountID).
		First(&existingItem).Error

	if err == nil {
		return fmt.Errorf("budget template item already exists for this account")
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing budget template item: %w", err)
	}

	// Create the item
	if err := s.db.Create(item).Error; err != nil {
		return fmt.Errorf("failed to create budget template item: %w", err)
	}

	return nil
}

// UpdateBudgetTemplateItem updates an existing template item
func (s *BudgetTemplateService) UpdateBudgetTemplateItem(id string, updates *models.BudgetTemplateItem) (*models.BudgetTemplateItem, error) {
	// Check if item exists
	var existingItem models.BudgetTemplateItem
	if err := s.db.Where("id = ?", id).First(&existingItem).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("budget template item not found")
		}
		return nil, fmt.Errorf("failed to fetch budget template item: %w", err)
	}

	// Validate updates
	if err := s.ValidateBudgetTemplateItemUpdates(updates); err != nil {
		return nil, err
	}

	// Update the item
	if err := s.db.Model(&existingItem).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update budget template item: %w", err)
	}

	// Fetch and return updated item
	var updatedItem models.BudgetTemplateItem
	if err := s.db.Preload("Template").Preload("Account").Where("id = ?", id).First(&updatedItem).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch updated budget template item: %w", err)
	}

	return &updatedItem, nil
}

// DeleteBudgetTemplateItem deletes a template item
func (s *BudgetTemplateService) DeleteBudgetTemplateItem(id string) error {
	// Check if item exists
	var item models.BudgetTemplateItem
	if err := s.db.Where("id = ?", id).First(&item).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("budget template item not found")
		}
		return fmt.Errorf("failed to fetch budget template item: %w", err)
	}

	// Delete the item
	if err := s.db.Delete(&item).Error; err != nil {
		return fmt.Errorf("failed to delete budget template item: %w", err)
	}

	return nil
}

// updateBudgetTemplateTransaction updates budget template in a transaction
func (s *BudgetTemplateService) updateBudgetTemplateTransaction(id string, updates *models.BudgetTemplate) (*models.BudgetTemplate, error) {
	var updatedTemplate models.BudgetTemplate

	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Update the budget template
		if err := tx.Model(&models.BudgetTemplate{}).Where("id = ?", id).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update budget template: %w", err)
		}

		// If items are provided, update them
		if len(updates.Items) > 0 {
			// Delete existing items
			if err := tx.Where("template_id = ?", id).Delete(&models.BudgetTemplateItem{}).Error; err != nil {
				return fmt.Errorf("failed to delete existing items: %w", err)
			}

			// Create new items
			for i := range updates.Items {
				updates.Items[i].TemplateID = id
			}
			if err := tx.Create(&updates.Items).Error; err != nil {
				return fmt.Errorf("failed to create new items: %w", err)
			}
		}

		// Fetch updated template
		if err := tx.Preload("Branch").Preload("Items").Preload("Items.Account").
			Where("id = ?", id).First(&updatedTemplate).Error; err != nil {
			return fmt.Errorf("failed to fetch updated budget template: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &updatedTemplate, nil
}

// ValidateBudgetTemplate validates budget template data
func (s *BudgetTemplateService) ValidateBudgetTemplate(template *models.BudgetTemplate) error {
	if template.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if template.Name == "" {
		return fmt.Errorf("name is required")
	}

	// Validate items if provided
	for i, item := range template.Items {
		if err := s.ValidateBudgetTemplateItem(&item); err != nil {
			return fmt.Errorf("item %d: %w", i+1, err)
		}
	}

	return nil
}

// ValidateBudgetTemplateUpdates validates budget template update data
func (s *BudgetTemplateService) ValidateBudgetTemplateUpdates(updates *models.BudgetTemplate) error {
	if updates.Name != "" && len(updates.Name) == 0 {
		return fmt.Errorf("name cannot be empty")
	}

	// Validate items if provided
	for i, item := range updates.Items {
		if err := s.ValidateBudgetTemplateItem(&item); err != nil {
			return fmt.Errorf("item %d: %w", i+1, err)
		}
	}

	return nil
}

// ValidateBudgetTemplateItem validates budget template item data
func (s *BudgetTemplateService) ValidateBudgetTemplateItem(item *models.BudgetTemplateItem) error {
	if item.AccountID == "" {
		return fmt.Errorf("account ID is required")
	}
	if item.Amount.LessThan(decimal.Zero) {
		return fmt.Errorf("amount cannot be negative")
	}

	return nil
}

// ValidateBudgetTemplateItemUpdates validates budget template item update data
func (s *BudgetTemplateService) ValidateBudgetTemplateItemUpdates(updates *models.BudgetTemplateItem) error {
	if !updates.Amount.IsZero() && updates.Amount.LessThan(decimal.Zero) {
		return fmt.Errorf("amount cannot be negative")
	}

	return nil
}

// ExportBudgetTemplate exports a budget template in the specified format
func (s *BudgetTemplateService) ExportBudgetTemplate(template *models.BudgetTemplate, format string) ([]byte, error) {
	// Generate export data based on format
	switch format {
	case "csv":
		return s.generateTemplateCSVExport(template)
	case "excel":
		return s.generateTemplateExcelExport(template)
	case "pdf":
		return s.generateTemplatePDFExport(template)
	default:
		return nil, fmt.Errorf("unsupported export format: %s", format)
	}
}

// DuplicateBudgetTemplate creates a duplicate of an existing budget template
func (s *BudgetTemplateService) DuplicateBudgetTemplate(templateID, newName string) (*models.BudgetTemplate, error) {
	// Get the original template with items
	originalTemplate, err := s.GetBudgetTemplateByID(templateID)
	if err != nil {
		return nil, err
	}

	// Create new template
	newTemplate := &models.BudgetTemplate{
		BranchID:    originalTemplate.BranchID,
		Name:        newName,
		Description: originalTemplate.Description,
		IsActive:    originalTemplate.IsActive,
	}

	// Duplicate items
	var newItems []models.BudgetTemplateItem
	for _, item := range originalTemplate.Items {
		newItem := models.BudgetTemplateItem{
			AccountID: item.AccountID,
			Amount:    item.Amount,
			Notes:     item.Notes,
		}
		newItems = append(newItems, newItem)
	}
	newTemplate.Items = newItems

	// Create the duplicated template
	if err := s.CreateBudgetTemplate(newTemplate); err != nil {
		return nil, fmt.Errorf("failed to create duplicate template: %w", err)
	}

	return newTemplate, nil
}

// Helper methods for template export

// generateTemplateCSVExport generates CSV export data for a template
func (s *BudgetTemplateService) generateTemplateCSVExport(template *models.BudgetTemplate) ([]byte, error) {
	var buf bytes.Buffer
	writer := csv.NewWriter(&buf)

	// Write template metadata
	metadata := [][]string{
		{"Template Name", template.Name},
		{"Description", func() string {
			if template.Description != nil {
				return *template.Description
			}
			return ""
		}()},
		{"Active", fmt.Sprintf("%t", template.IsActive)},
		{"Created", template.CreatedAt.Format("2006-01-02 15:04:05")},
		{"Items Count", fmt.Sprintf("%d", len(template.Items))},
		{"", ""}, // Empty row
	}

	for _, row := range metadata {
		if err := writer.Write(row); err != nil {
			return nil, fmt.Errorf("failed to write metadata row: %w", err)
		}
	}

	// Write header for items
	header := []string{"Account ID", "Account Name", "Account Code", "Amount", "Notes"}
	if err := writer.Write(header); err != nil {
		return nil, fmt.Errorf("failed to write CSV header: %w", err)
	}

	// Write template items
	for _, item := range template.Items {
		accountName := ""
		accountCode := ""
		if item.Account.ID != "" {
			accountName = item.Account.Name
			accountCode = item.Account.Code
		}

		amount, _ := item.Amount.Float64()
		notes := ""
		if item.Notes != nil {
			notes = *item.Notes
		}

		row := []string{
			item.AccountID,
			accountName,
			accountCode,
			fmt.Sprintf("%.2f", amount),
			notes,
		}

		if err := writer.Write(row); err != nil {
			return nil, fmt.Errorf("failed to write CSV row: %w", err)
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, fmt.Errorf("failed to flush CSV writer: %w", err)
	}

	return buf.Bytes(), nil
}

// generateTemplateExcelExport generates Excel export data for a template (placeholder)
func (s *BudgetTemplateService) generateTemplateExcelExport(template *models.BudgetTemplate) ([]byte, error) {
	// For now, return CSV data as Excel is more complex to implement
	// In a real implementation, you would use a library like excelize
	return s.generateTemplateCSVExport(template)
}

// generateTemplatePDFExport generates PDF export data for a template (placeholder)
func (s *BudgetTemplateService) generateTemplatePDFExport(template *models.BudgetTemplate) ([]byte, error) {
	// For now, return CSV data as PDF generation is complex
	// In a real implementation, you would use a library like gofpdf
	return s.generateTemplateCSVExport(template)
}

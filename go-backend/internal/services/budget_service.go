package services

import (
	"bytes"
	"encoding/csv"
	"errors"
	"fmt"
	"strconv"
	"time"

	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// BudgetService handles budget-related business logic
type BudgetService struct {
	db *gorm.DB
}

// NewBudgetService creates a new BudgetService
func NewBudgetService(db *gorm.DB) *BudgetService {
	return &BudgetService{db: db}
}

// GetAllBudgetItems retrieves all budget items with pagination
func (s *BudgetService) GetAllBudgetItems(page, limit int, search string) ([]models.BudgetItem, int64, error) {
	var budgetItems []models.BudgetItem
	var total int64

	query := s.db.Model(&models.BudgetItem{})

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN chart_of_accounts ON budget_items.account_id = chart_of_accounts.id").
			Where("chart_of_accounts.name ILIKE ? OR chart_of_accounts.code ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Account").
		Offset(offset).Limit(limit).
		Order("year DESC, month DESC, created_at DESC").
		Find(&budgetItems).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, total, nil
}

// GetBudgetItemByID retrieves a budget item by ID
func (s *BudgetService) GetBudgetItemByID(id string) (*models.BudgetItem, error) {
	var budgetItem models.BudgetItem

	if err := s.db.Preload("Branch").Preload("Account").
		First(&budgetItem, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("budget item not found")
		}
		return nil, fmt.Errorf("failed to fetch budget item: %w", err)
	}

	return &budgetItem, nil
}

// GetBudgetItemsByMerchant retrieves budget items for a specific merchant
func (s *BudgetService) GetBudgetItemsByMerchant(merchantID string, page, limit int, search, accountID string, year, month *int) ([]models.BudgetItem, int64, error) {
	var budgetItems []models.BudgetItem
	var total int64

	query := s.db.Model(&models.BudgetItem{}).Where("merchant_id = ?", merchantID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN chart_of_accounts ON budget_items.account_id = chart_of_accounts.id").
			Where("chart_of_accounts.name ILIKE ? OR chart_of_accounts.code ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply account filter if provided
	if accountID != "" {
		query = query.Where("account_id = ?", accountID)
	}

	// Apply year filter if provided
	if year != nil {
		query = query.Where("year = ?", *year)
	}

	// Apply month filter if provided
	if month != nil {
		query = query.Where("month = ?", *month)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Account").
		Offset(offset).Limit(limit).
		Order("year DESC, month DESC, created_at DESC").
		Find(&budgetItems).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, total, nil
}

// GetBudgetItemsByAccount retrieves budget items for a specific account
func (s *BudgetService) GetBudgetItemsByAccount(accountID string, page, limit int, year, month *int) ([]models.BudgetItem, int64, error) {
	var budgetItems []models.BudgetItem
	var total int64

	query := s.db.Model(&models.BudgetItem{}).Where("account_id = ?", accountID)

	// Apply year filter if provided
	if year != nil {
		query = query.Where("year = ?", *year)
	}

	// Apply month filter if provided
	if month != nil {
		query = query.Where("month = ?", *month)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Account").
		Offset(offset).Limit(limit).
		Order("year DESC, month DESC, created_at DESC").
		Find(&budgetItems).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, total, nil
}

// GetBudgetItemsByBranch retrieves budget items for a specific branch
func (s *BudgetService) GetBudgetItemsByBranch(branchID string, page, limit int, search, accountID string, year, month *int) ([]models.BudgetItem, int64, error) {
	var budgetItems []models.BudgetItem
	var total int64

	query := s.db.Model(&models.BudgetItem{}).Where("branch_id = ?", branchID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN chart_of_accounts ON budget_items.account_id = chart_of_accounts.id").
			Where("chart_of_accounts.name ILIKE ? OR chart_of_accounts.code ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply account filter if provided
	if accountID != "" {
		query = query.Where("account_id = ?", accountID)
	}

	// Apply year filter if provided
	if year != nil {
		query = query.Where("year = ?", *year)
	}

	// Apply month filter if provided
	if month != nil {
		query = query.Where("month = ?", *month)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Account").
		Offset(offset).Limit(limit).
		Order("year DESC, month DESC, created_at DESC").
		Find(&budgetItems).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, total, nil
}

// GetBudgetItemsByOrganization retrieves budget items for all branches in an organization
func (s *BudgetService) GetBudgetItemsByOrganization(organizationID string, page, limit int, search, accountID string, year, month *int) ([]models.BudgetItem, int64, error) {
	var budgetItems []models.BudgetItem
	var total int64

	// Join with branches to filter by organization
	query := s.db.Model(&models.BudgetItem{}).
		Joins("JOIN branches ON budget_items.branch_id = branches.id").
		Where("branches.organization_id = ?", organizationID)

	// Apply search filter if provided
	if search != "" {
		query = query.Joins("LEFT JOIN chart_of_accounts ON budget_items.account_id = chart_of_accounts.id").
			Where("chart_of_accounts.name ILIKE ? OR chart_of_accounts.code ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply account filter if provided
	if accountID != "" {
		query = query.Where("budget_items.account_id = ?", accountID)
	}

	// Apply year filter if provided
	if year != nil {
		query = query.Where("budget_items.year = ?", *year)
	}

	// Apply month filter if provided
	if month != nil {
		query = query.Where("budget_items.month = ?", *month)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * limit
	if err := query.Preload("Branch").Preload("Account").
		Offset(offset).Limit(limit).
		Order("budget_items.year DESC, budget_items.month DESC, budget_items.created_at DESC").
		Find(&budgetItems).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, total, nil
}

// CreateBudgetItem creates a new budget item
func (s *BudgetService) CreateBudgetItem(budgetItem *models.BudgetItem) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Validate branch exists
		var branch models.Branch
		if err := tx.First(&branch, "id = ?", budgetItem.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("branch not found")
			}
			return fmt.Errorf("failed to validate branch: %w", err)
		}

		// Validate account exists and belongs to branch
		var account models.ChartOfAccount
		if err := tx.First(&account, "id = ? AND branch_id = ?", budgetItem.AccountID, budgetItem.BranchID).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("account not found or does not belong to branch")
			}
			return fmt.Errorf("failed to validate account: %w", err)
		}

		// Check if budget item already exists for this branch, account, year, and month
		var existingItem models.BudgetItem
		if err := tx.First(&existingItem, "branch_id = ? AND account_id = ? AND year = ? AND month = ?",
			budgetItem.BranchID, budgetItem.AccountID, budgetItem.Year, budgetItem.Month).Error; err == nil {
			return fmt.Errorf("budget item already exists for this account, year, and month")
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("failed to check existing budget item: %w", err)
		}

		// Validate budget item
		if err := s.ValidateBudgetItem(budgetItem); err != nil {
			return err
		}

		// Create the budget item
		if err := tx.Create(budgetItem).Error; err != nil {
			return fmt.Errorf("failed to create budget item: %w", err)
		}

		return nil
	})
}

// UpdateBudgetItem updates an existing budget item
func (s *BudgetService) UpdateBudgetItem(id string, updates *models.BudgetItem) (*models.BudgetItem, error) {
	var budgetItem models.BudgetItem

	// Check if budget item exists
	if err := s.db.First(&budgetItem, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("budget item not found")
		}
		return nil, fmt.Errorf("failed to fetch budget item: %w", err)
	}

	var updatedItem *models.BudgetItem
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// If updating account, year, or month, check for duplicates
		if (updates.AccountID != "" && updates.AccountID != budgetItem.AccountID) ||
			(updates.Year != 0 && updates.Year != budgetItem.Year) ||
			(updates.Month != 0 && updates.Month != budgetItem.Month) {

			accountID := budgetItem.AccountID
			year := budgetItem.Year
			month := budgetItem.Month

			if updates.AccountID != "" {
				accountID = updates.AccountID
			}
			if updates.Year != 0 {
				year = updates.Year
			}
			if updates.Month != 0 {
				month = updates.Month
			}

			var existingItem models.BudgetItem
			if err := tx.Where("branch_id = ? AND account_id = ? AND year = ? AND month = ? AND id != ?",
				budgetItem.BranchID, accountID, year, month, id).
				First(&existingItem).Error; err == nil {
				return fmt.Errorf("budget item already exists for this account, year, and month")
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("failed to check existing budget item: %w", err)
			}
		}

		// Update the budget item
		if err := tx.Model(&budgetItem).Updates(updates).Error; err != nil {
			return fmt.Errorf("failed to update budget item: %w", err)
		}

		// Fetch updated budget item with relationships
		if err := tx.Preload("Branch").Preload("Account").
			First(&budgetItem, "id = ?", id).Error; err != nil {
			return fmt.Errorf("failed to fetch updated budget item: %w", err)
		}

		updatedItem = &budgetItem
		return nil
	})

	if err != nil {
		return nil, err
	}

	return updatedItem, nil
}

// DeleteBudgetItem deletes a budget item
func (s *BudgetService) DeleteBudgetItem(id string) error {
	var budgetItem models.BudgetItem

	// Check if budget item exists
	if err := s.db.First(&budgetItem, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("budget item not found")
		}
		return fmt.Errorf("failed to fetch budget item: %w", err)
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Delete the budget item
		if err := tx.Delete(&budgetItem).Error; err != nil {
			return fmt.Errorf("failed to delete budget item: %w", err)
		}

		return nil
	})
}

// GetBudgetReport generates a budget report for a specific period
func (s *BudgetService) GetBudgetReport(branchID string, year, month int) (*BudgetReport, error) {
	var report BudgetReport

	// Get budget items for the period
	var budgetItems []models.BudgetItem
	if err := s.db.Preload("Account").
		Where("branch_id = ? AND year = ? AND month = ?", branchID, year, month).
		Find(&budgetItems).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	// Calculate totals
	var totalBudgeted, totalActual decimal.Decimal
	var reportItems []BudgetReportItem

	for _, item := range budgetItems {
		totalBudgeted = totalBudgeted.Add(item.Amount)
		totalActual = totalActual.Add(item.ActualAmount)

		variance := item.ActualAmount.Sub(item.Amount)
		variancePercent := decimal.Zero
		if !item.Amount.IsZero() {
			variancePercent = variance.Div(item.Amount).Mul(decimal.NewFromInt(100))
		}

		reportItems = append(reportItems, BudgetReportItem{
			AccountID:       item.AccountID,
			AccountName:     item.Account.Name,
			AccountCode:     item.Account.Code,
			BudgetedAmount:  item.Amount,
			ActualAmount:    item.ActualAmount,
			Variance:        variance,
			VariancePercent: variancePercent,
		})
	}

	// Calculate overall variance
	totalVariance := totalActual.Sub(totalBudgeted)
	totalVariancePercent := decimal.Zero
	if !totalBudgeted.IsZero() {
		totalVariancePercent = totalVariance.Div(totalBudgeted).Mul(decimal.NewFromInt(100))
	}

	report = BudgetReport{
		BranchID:             branchID,
		Year:                 year,
		Month:                month,
		TotalBudgeted:        totalBudgeted,
		TotalActual:          totalActual,
		TotalVariance:        totalVariance,
		TotalVariancePercent: totalVariancePercent,
		Items:                reportItems,
		GeneratedAt:          time.Now(),
	}

	return &report, nil
}

// GetBudgetReportByOrganization generates a budget report for all branches in an organization
func (s *BudgetService) GetBudgetReportByOrganization(organizationID string, year, month int) (*BudgetReport, error) {
	var report BudgetReport

	// Get budget items for all branches in the organization
	var budgetItems []models.BudgetItem
	if err := s.db.Preload("Account").Preload("Branch").
		Joins("JOIN branches ON budget_items.branch_id = branches.id").
		Where("branches.organization_id = ? AND budget_items.year = ? AND budget_items.month = ?", organizationID, year, month).
		Find(&budgetItems).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	// Calculate totals
	var totalBudgeted, totalActual decimal.Decimal
	var reportItems []BudgetReportItem

	for _, item := range budgetItems {
		totalBudgeted = totalBudgeted.Add(item.Amount)
		totalActual = totalActual.Add(item.ActualAmount)

		variance := item.ActualAmount.Sub(item.Amount)
		variancePercent := decimal.Zero
		if !item.Amount.IsZero() {
			variancePercent = variance.Div(item.Amount).Mul(decimal.NewFromInt(100))
		}

		reportItems = append(reportItems, BudgetReportItem{
			AccountID:       item.AccountID,
			AccountName:     item.Account.Name,
			AccountCode:     item.Account.Code,
			BudgetedAmount:  item.Amount,
			ActualAmount:    item.ActualAmount,
			Variance:        variance,
			VariancePercent: variancePercent,
		})
	}

	// Calculate overall variance
	totalVariance := totalActual.Sub(totalBudgeted)
	totalVariancePercent := decimal.Zero
	if !totalBudgeted.IsZero() {
		totalVariancePercent = totalVariance.Div(totalBudgeted).Mul(decimal.NewFromInt(100))
	}

	report = BudgetReport{
		BranchID:             organizationID, // Using organizationID as identifier
		Year:                 year,
		Month:                month,
		TotalBudgeted:        totalBudgeted,
		TotalActual:          totalActual,
		TotalVariance:        totalVariance,
		TotalVariancePercent: totalVariancePercent,
		Items:                reportItems,
		GeneratedAt:          time.Now(),
	}

	return &report, nil
}

// GetBudgetSummary gets summary statistics for budget items
func (s *BudgetService) GetBudgetSummary(branchID string) (*BudgetSummary, error) {
	var summary BudgetSummary

	// Get total count
	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ?", branchID).
		Count(&summary.TotalBudgetItems).Error; err != nil {
		return nil, fmt.Errorf("failed to count budget items: %w", err)
	}

	// Get current year budget items
	currentYear := time.Now().Year()
	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ? AND year = ?", branchID, currentYear).
		Count(&summary.CurrentYearItems).Error; err != nil {
		return nil, fmt.Errorf("failed to count current year budget items: %w", err)
	}

	// Get budget by year
	yearCounts := []struct {
		Year  int
		Count int64
	}{}

	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ?", branchID).
		Select("year, COUNT(*) as count").
		Group("year").
		Order("year DESC").
		Scan(&yearCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get year counts: %w", err)
	}

	summary.YearCounts = make(map[int]int64)
	for _, yc := range yearCounts {
		summary.YearCounts[yc.Year] = yc.Count
	}

	// Get budget by account
	accountCounts := []struct {
		AccountID string
		Count     int64
	}{}

	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ?", branchID).
		Select("account_id, COUNT(*) as count").
		Group("account_id").
		Scan(&accountCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get account counts: %w", err)
	}

	summary.AccountCounts = make(map[string]int64)
	for _, ac := range accountCounts {
		summary.AccountCounts[ac.AccountID] = ac.Count
	}

	// Get total budgeted amount for current year
	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ? AND year = ?", branchID, currentYear).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&summary.TotalBudgetedCurrentYear).Error; err != nil {
		return nil, fmt.Errorf("failed to sum budgeted amounts: %w", err)
	}

	// Get total actual amount for current year
	if err := s.db.Model(&models.BudgetItem{}).
		Where("branch_id = ? AND year = ?", branchID, currentYear).
		Select("COALESCE(SUM(actual_amount), 0)").
		Scan(&summary.TotalActualCurrentYear).Error; err != nil {
		return nil, fmt.Errorf("failed to sum actual amounts: %w", err)
	}

	return &summary, nil
}

// BulkDeleteBudgetItems deletes multiple budget items by IDs
func (s *BudgetService) BulkDeleteBudgetItems(ids []string) (int64, error) {
	if len(ids) == 0 {
		return 0, fmt.Errorf("no IDs provided")
	}

	result := s.db.Where("id IN ?", ids).Delete(&models.BudgetItem{})
	if result.Error != nil {
		return 0, fmt.Errorf("failed to delete budget items: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// BulkUpdateBudgetItems updates multiple budget items
func (s *BudgetService) BulkUpdateBudgetItems(ids []string, updates map[string]interface{}) (int64, error) {
	if len(ids) == 0 {
		return 0, fmt.Errorf("no IDs provided")
	}

	if len(updates) == 0 {
		return 0, fmt.Errorf("no updates provided")
	}

	// Validate and sanitize updates
	allowedFields := map[string]bool{
		"amount": true,
		"notes":  true,
	}

	sanitizedUpdates := make(map[string]interface{})
	for key, value := range updates {
		if allowedFields[key] {
			sanitizedUpdates[key] = value
		}
	}

	if len(sanitizedUpdates) == 0 {
		return 0, fmt.Errorf("no valid updates provided")
	}

	result := s.db.Model(&models.BudgetItem{}).Where("id IN ?", ids).Updates(sanitizedUpdates)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to update budget items: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// ExportBudgetItems exports budget items in specified format
func (s *BudgetService) ExportBudgetItems(ids []string, format, branchID, organizationID string, year, month *int) ([]byte, error) {
	var budgetItems []models.BudgetItem
	var err error

	query := s.db.Preload("Account").Preload("Branch")

	// Filter by IDs if provided
	if len(ids) > 0 {
		query = query.Where("id IN ?", ids)
	}

	// Apply context filters
	if branchID != "" {
		query = query.Where("branch_id = ?", branchID)
	} else if organizationID != "" {
		query = query.Joins("JOIN branches ON budget_items.branch_id = branches.id").
			Where("branches.organization_id = ?", organizationID)
	}

	// Apply period filters
	if year != nil {
		query = query.Where("year = ?", *year)
	}
	if month != nil {
		query = query.Where("month = ?", *month)
	}

	if err = query.Find(&budgetItems).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	// Generate export data based on format
	switch format {
	case "csv":
		return s.generateCSVExport(budgetItems)
	case "excel":
		return s.generateExcelExport(budgetItems)
	case "pdf":
		return s.generatePDFExport(budgetItems)
	default:
		return nil, fmt.Errorf("unsupported export format: %s", format)
	}
}

// GetBudgetComparison compares budget between two periods
func (s *BudgetService) GetBudgetComparison(currentYear, currentMonth, previousYear, previousMonth int, branchID, organizationID string) (*BudgetComparison, error) {
	// Get current period budget items
	currentItems, err := s.getBudgetItemsForPeriod(currentYear, currentMonth, branchID, organizationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current period budget items: %w", err)
	}

	// Get previous period budget items
	previousItems, err := s.getBudgetItemsForPeriod(previousYear, previousMonth, branchID, organizationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get previous period budget items: %w", err)
	}

	// Calculate totals
	currentTotal := s.calculateTotal(currentItems)
	previousTotal := s.calculateTotal(previousItems)

	// Calculate variance
	variance := currentTotal - previousTotal
	variancePercent := float64(0)
	if previousTotal != 0 {
		variancePercent = (variance / previousTotal) * 100
	}

	// Generate account-level comparisons
	accountComparisons := s.generateAccountComparisons(currentItems, previousItems)

	return &BudgetComparison{
		CurrentPeriod: BudgetPeriod{
			Year:  currentYear,
			Month: currentMonth,
			Total: currentTotal,
		},
		PreviousPeriod: BudgetPeriod{
			Year:  previousYear,
			Month: previousMonth,
			Total: previousTotal,
		},
		Variance:        variance,
		VariancePercent: variancePercent,
		Items:           accountComparisons,
	}, nil
}

// ExportBudget exports all budget data for a period
func (s *BudgetService) ExportBudget(year, month int, format, branchID, organizationID string) ([]byte, error) {
	return s.ExportBudgetItems(nil, format, branchID, organizationID, &year, &month)
}

// ImportBudget imports budget data from file
func (s *BudgetService) ImportBudget(file interface{}, filename, branchID, organizationID string) (*ImportResult, error) {
	// This is a placeholder implementation
	// In a real implementation, you would parse the file based on its type
	// and create budget items from the data

	return &ImportResult{
		Created: 0,
		Updated: 0,
		Skipped: 0,
		Errors:  []string{},
		Message: "Import functionality not yet implemented",
	}, nil
}

// UpdateActualAmounts updates actual amounts for budget items based on transactions
func (s *BudgetService) UpdateActualAmounts(branchID string, year, month int) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Get budget items for the period
		var budgetItems []models.BudgetItem
		if err := tx.Where("branch_id = ? AND year = ? AND month = ?", branchID, year, month).
			Find(&budgetItems).Error; err != nil {
			return fmt.Errorf("failed to fetch budget items: %w", err)
		}

		// Calculate start and end dates for the month
		startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
		endDate := startDate.AddDate(0, 1, -1)

		for _, item := range budgetItems {
			// Calculate actual amount from journal entries
			var actualAmount decimal.Decimal

			// Sum debit amounts for the account in the period
			var debitSum decimal.Decimal
			if err := tx.Model(&models.JournalEntry{}).
				Where("branch_id = ? AND account_id = ? AND entry_date BETWEEN ? AND ?",
					branchID, item.AccountID, startDate, endDate).
				Select("COALESCE(SUM(debit_amount), 0)").
				Scan(&debitSum).Error; err != nil {
				return fmt.Errorf("failed to sum debit amounts: %w", err)
			}

			// Sum credit amounts for the account in the period
			var creditSum decimal.Decimal
			if err := tx.Model(&models.JournalEntry{}).
				Where("branch_id = ? AND account_id = ? AND entry_date BETWEEN ? AND ?",
					branchID, item.AccountID, startDate, endDate).
				Select("COALESCE(SUM(credit_amount), 0)").
				Scan(&creditSum).Error; err != nil {
				return fmt.Errorf("failed to sum credit amounts: %w", err)
			}

			// Calculate net amount based on account type
			// For expense accounts, actual = debit - credit
			// For revenue accounts, actual = credit - debit
			// This is a simplified calculation - in reality, you'd need to check account type
			actualAmount = debitSum.Sub(creditSum)

			// Update the budget item with actual amount
			if err := tx.Model(&item).Update("actual_amount", actualAmount).Error; err != nil {
				return fmt.Errorf("failed to update actual amount: %w", err)
			}
		}

		return nil
	})
}

// ValidateBudgetItem validates budget item data
func (s *BudgetService) ValidateBudgetItem(budgetItem *models.BudgetItem) error {
	if budgetItem.BranchID == "" {
		return fmt.Errorf("branch ID is required")
	}
	if budgetItem.AccountID == "" {
		return fmt.Errorf("account ID is required")
	}
	if budgetItem.Year < 1900 || budgetItem.Year > 2100 {
		return fmt.Errorf("year must be between 1900 and 2100")
	}
	if budgetItem.Month < 1 || budgetItem.Month > 12 {
		return fmt.Errorf("month must be between 1 and 12")
	}
	if budgetItem.Amount.LessThan(decimal.Zero) {
		return fmt.Errorf("budget amount cannot be negative")
	}

	return nil
}

// BudgetReport represents a budget report for a specific period
type BudgetReport struct {
	BranchID             string             `json:"branchId"`
	Year                 int                `json:"year"`
	Month                int                `json:"month"`
	TotalBudgeted        decimal.Decimal    `json:"totalBudgeted"`
	TotalActual          decimal.Decimal    `json:"totalActual"`
	TotalVariance        decimal.Decimal    `json:"totalVariance"`
	TotalVariancePercent decimal.Decimal    `json:"totalVariancePercent"`
	Items                []BudgetReportItem `json:"items"`
	GeneratedAt          time.Time          `json:"generatedAt"`
}

// BudgetReportItem represents a single item in a budget report
type BudgetReportItem struct {
	AccountID       string          `json:"accountId"`
	AccountName     string          `json:"accountName"`
	AccountCode     string          `json:"accountCode"`
	BudgetedAmount  decimal.Decimal `json:"budgetedAmount"`
	ActualAmount    decimal.Decimal `json:"actualAmount"`
	Variance        decimal.Decimal `json:"variance"`
	VariancePercent decimal.Decimal `json:"variancePercent"`
}

// BudgetSummary represents summary statistics for budget items
type BudgetSummary struct {
	TotalBudgetItems         int64            `json:"totalBudgetItems"`
	CurrentYearItems         int64            `json:"currentYearItems"`
	YearCounts               map[int]int64    `json:"yearCounts"`
	AccountCounts            map[string]int64 `json:"accountCounts"`
	TotalBudgetedCurrentYear decimal.Decimal  `json:"totalBudgetedCurrentYear"`
	TotalActualCurrentYear   decimal.Decimal  `json:"totalActualCurrentYear"`
}

// BudgetComparison represents a comparison between two budget periods
type BudgetComparison struct {
	CurrentPeriod   BudgetPeriod           `json:"currentPeriod"`
	PreviousPeriod  BudgetPeriod           `json:"previousPeriod"`
	Variance        float64                `json:"variance"`
	VariancePercent float64                `json:"variancePercent"`
	Items           []BudgetComparisonItem `json:"items"`
}

// BudgetPeriod represents a budget period summary
type BudgetPeriod struct {
	Year  int     `json:"year"`
	Month int     `json:"month"`
	Total float64 `json:"total"`
}

// BudgetComparisonItem represents an account-level comparison
type BudgetComparisonItem struct {
	AccountID       string  `json:"accountId"`
	AccountName     string  `json:"accountName"`
	Current         float64 `json:"current"`
	Previous        float64 `json:"previous"`
	Variance        float64 `json:"variance"`
	VariancePercent float64 `json:"variancePercent"`
}

// ImportResult represents the result of a budget import operation
type ImportResult struct {
	Created int      `json:"created"`
	Updated int      `json:"updated"`
	Skipped int      `json:"skipped"`
	Errors  []string `json:"errors"`
	Message string   `json:"message"`
}

// Helper methods for budget comparison and export

// getBudgetItemsForPeriod retrieves budget items for a specific period
func (s *BudgetService) getBudgetItemsForPeriod(year, month int, branchID, organizationID string) ([]models.BudgetItem, error) {
	var budgetItems []models.BudgetItem

	query := s.db.Preload("Account").Preload("Branch").
		Where("year = ? AND month = ?", year, month)

	if branchID != "" {
		query = query.Where("branch_id = ?", branchID)
	} else if organizationID != "" {
		query = query.Joins("JOIN branches ON budget_items.branch_id = branches.id").
			Where("branches.organization_id = ?", organizationID)
	}

	if err := query.Find(&budgetItems).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch budget items: %w", err)
	}

	return budgetItems, nil
}

// calculateTotal calculates the total amount from budget items
func (s *BudgetService) calculateTotal(items []models.BudgetItem) float64 {
	var total float64
	for _, item := range items {
		amount, _ := item.Amount.Float64()
		total += amount
	}
	return total
}

// generateAccountComparisons generates account-level comparisons
func (s *BudgetService) generateAccountComparisons(currentItems, previousItems []models.BudgetItem) []BudgetComparisonItem {
	// Create maps for easier lookup
	currentMap := make(map[string]float64)
	previousMap := make(map[string]float64)
	accountNames := make(map[string]string)

	// Process current items
	for _, item := range currentItems {
		amount, _ := item.Amount.Float64()
		currentMap[item.AccountID] += amount
		if item.Account.ID != "" {
			accountNames[item.AccountID] = item.Account.Name
		}
	}

	// Process previous items
	for _, item := range previousItems {
		amount, _ := item.Amount.Float64()
		previousMap[item.AccountID] += amount
		if item.Account.ID != "" {
			accountNames[item.AccountID] = item.Account.Name
		}
	}

	// Get all unique account IDs
	allAccountIDs := make(map[string]bool)
	for accountID := range currentMap {
		allAccountIDs[accountID] = true
	}
	for accountID := range previousMap {
		allAccountIDs[accountID] = true
	}

	// Generate comparisons
	var comparisons []BudgetComparisonItem
	for accountID := range allAccountIDs {
		current := currentMap[accountID]
		previous := previousMap[accountID]
		variance := current - previous
		variancePercent := float64(0)

		if previous != 0 {
			variancePercent = (variance / previous) * 100
		}

		accountName := accountNames[accountID]
		if accountName == "" {
			accountName = "Unknown Account"
		}

		comparisons = append(comparisons, BudgetComparisonItem{
			AccountID:       accountID,
			AccountName:     accountName,
			Current:         current,
			Previous:        previous,
			Variance:        variance,
			VariancePercent: variancePercent,
		})
	}

	return comparisons
}

// generateCSVExport generates CSV export data
func (s *BudgetService) generateCSVExport(items []models.BudgetItem) ([]byte, error) {
	var buf bytes.Buffer
	writer := csv.NewWriter(&buf)

	// Write header
	header := []string{"Account ID", "Account Name", "Account Code", "Year", "Month", "Amount", "Notes", "Created At"}
	if err := writer.Write(header); err != nil {
		return nil, fmt.Errorf("failed to write CSV header: %w", err)
	}

	// Write data rows
	for _, item := range items {
		accountName := ""
		accountCode := ""
		if item.Account.ID != "" {
			accountName = item.Account.Name
			accountCode = item.Account.Code
		}

		amount, _ := item.Amount.Float64()
		notes := ""
		if item.Notes != nil {
			notes = *item.Notes
		}

		row := []string{
			item.AccountID,
			accountName,
			accountCode,
			strconv.Itoa(item.Year),
			strconv.Itoa(item.Month),
			fmt.Sprintf("%.2f", amount),
			notes,
			item.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		if err := writer.Write(row); err != nil {
			return nil, fmt.Errorf("failed to write CSV row: %w", err)
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, fmt.Errorf("failed to flush CSV writer: %w", err)
	}

	return buf.Bytes(), nil
}

// generateExcelExport generates Excel export data (placeholder)
func (s *BudgetService) generateExcelExport(items []models.BudgetItem) ([]byte, error) {
	// For now, return CSV data as Excel is more complex to implement
	// In a real implementation, you would use a library like excelize
	return s.generateCSVExport(items)
}

// generatePDFExport generates PDF export data (placeholder)
func (s *BudgetService) generatePDFExport(items []models.BudgetItem) ([]byte, error) {
	// For now, return CSV data as PDF generation is complex
	// In a real implementation, you would use a library like gofpdf
	return s.generateCSVExport(items)
}

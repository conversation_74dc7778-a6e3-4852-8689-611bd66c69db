package database

import (
	"fmt"
	"log"

	"adc-account-backend/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func Initialize(databaseURL string) (*gorm.DB, error) {
	db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Test the connection
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database instance: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connection established successfully")

	// Auto-migrate models
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to auto-migrate: %w", err)
	}

	// Run custom schema fixes for merchant -> branch migration
	if err := runSchemaFixes(db); err != nil {
		return nil, fmt.Errorf("schema fixes failed: %w", err)
	}

	return db, nil
}

func autoMigrate(db *gorm.DB) error {
	log.Println("Running auto-migration...")

	// Define the order of migration to handle foreign key dependencies
	modelsToMigrate := []interface{}{
		// Core models first
		&models.User{},
		&models.Organization{},
		&models.Branch{},
		&models.Merchant{},

		// Permission models
		&models.UserOrganizationPermission{},
		&models.UserBranchPermission{},
		&models.UserMerchantPermission{},

		// Account and financial models
		&models.ChartOfAccount{},
		&models.TaxRate{},
		&models.BankAccount{},
		&models.BankTransaction{},
		&models.BankReconciliation{},
		&models.BankTransactionMatch{},

		// Customer and vendor models
		&models.Customer{},
		&models.Vendor{},

		// Invoice related models
		&models.Invoice{},
		&models.InvoiceItem{},
		&models.InvoicePayment{},
		&models.InvoiceTemplate{},
		&models.InvoiceTemplateItem{},
		&models.RecurringInvoice{},
		&models.RecurringInvoiceItem{},

		// Bill related models
		&models.Bill{},
		&models.BillItem{},
		&models.BillPayment{},

		// Credit and payment models
		&models.CreditNote{},
		&models.CreditNoteItem{},
		&models.CreditApplication{},
		&models.CustomerCredit{},
		&models.PaymentReminder{},
		&models.CustomerStatement{},
		&models.StatementItem{},

		// Asset models
		&models.Asset{},

		// Inventory models
		&models.InventoryItem{},
		&models.InventoryTransaction{},

		// Employee and payroll models
		&models.Employee{},
		&models.PayrollRun{},
		&models.PayrollDetail{},

		// Journal entry models
		&models.JournalEntry{},
		&models.JournalEntryLine{},

		// Expense models
		&models.ExpenseCategory{},
		&models.Expense{},
		&models.ExpensePayment{},

		// Budget models
		&models.BudgetItem{},
		&models.BudgetTemplate{},
		&models.BudgetTemplateItem{},
		&models.BudgetIntegrationSettings{},
		&models.BudgetItemIntegration{},
		&models.BudgetForecast{},
		&models.BudgetAlert{},
		&models.BudgetIntegrationLog{},

		// Cash flow models
		&models.CashFlowItem{},
		&models.RecurringCashFlowItem{},
		&models.CashFlowCategory{},

		// Collection models
		&models.CollectionCase{},
		&models.CollectionActivity{},
		&models.CollectionTemplate{},
		&models.CollectionTemplateStep{},

		// Email models
		&models.EmailTemplate{},
		&models.EmailLog{},

		// Order models
		&models.SalesOrder{},

		// Tax models
		&models.TaxReport{},

		// Subscription models
		&models.Subscription{},
		&models.UsageRecord{},

		// API and audit models
		&models.ApiKey{},
		&models.AuditLog{},

		// Auth models
		&models.Account{},
		&models.Session{},
		&models.VerificationToken{},
		&models.RefreshToken{},
		&models.BlacklistedToken{},
		&models.LoginAttempt{},
		&models.PasswordReset{},
		&models.UserPreferences{},
	}

	for _, model := range modelsToMigrate {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate %T: %w", model, err)
		}
	}

	log.Println("Auto-migration completed successfully")
	return nil
}

// runSchemaFixes handles custom schema changes for merchant -> branch migration
func runSchemaFixes(db *gorm.DB) error {
	log.Println("Running schema fixes for merchant -> branch migration...")

	// List of tables that need merchant_id -> branch_id migration
	tablesToFix := []string{
		"chart_of_accounts",
		"customers",
		"vendors",
		"invoices",
		"bills",
		"expenses",
		"assets",
		"bank_accounts",
		"bank_reconciliations",
		"inventory_items",
		"employees",
		"journal_entries",
		"budget_items",
		"budget_templates", // Add budget_templates to migration list
		"cash_flow_items",
		"collection_cases",
		"sales_orders",
		"tax_reports",
	}

	for _, tableName := range tablesToFix {
		log.Printf("Fixing table: %s", tableName)

		// Check if table exists
		var exists bool
		err := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = CURRENT_SCHEMA() AND table_name = ?)", tableName).Scan(&exists).Error
		if err != nil {
			log.Printf("Error checking table %s: %v", tableName, err)
			continue
		}

		if !exists {
			log.Printf("Table %s does not exist, skipping", tableName)
			continue
		}

		// Check if merchant_id column exists
		var merchantIdExists bool
		err = db.Raw(`
			SELECT EXISTS (
				SELECT FROM information_schema.columns
				WHERE table_schema = CURRENT_SCHEMA()
				AND table_name = ?
				AND column_name = 'merchant_id'
			)`, tableName).Scan(&merchantIdExists).Error
		if err != nil {
			log.Printf("Error checking merchant_id column in %s: %v", tableName, err)
			continue
		}

		// Check if branch_id column exists
		var branchIdExists bool
		err = db.Raw(`
			SELECT EXISTS (
				SELECT FROM information_schema.columns
				WHERE table_schema = CURRENT_SCHEMA()
				AND table_name = ?
				AND column_name = 'branch_id'
			)`, tableName).Scan(&branchIdExists).Error
		if err != nil {
			log.Printf("Error checking branch_id column in %s: %v", tableName, err)
			continue
		}

		// Handle migration from merchant_id to branch_id
		if merchantIdExists && !branchIdExists {
			log.Printf("Migrating %s from merchant_id to branch_id...", tableName)

			// Add branch_id column
			err = db.Exec(fmt.Sprintf("ALTER TABLE %s ADD COLUMN branch_id VARCHAR(36)", tableName)).Error
			if err != nil {
				log.Printf("Warning: Could not add branch_id column to %s: %v", tableName, err)
				continue
			}

			// Copy data from merchant_id to branch_id (assuming 1:1 mapping)
			err = db.Exec(fmt.Sprintf("UPDATE %s SET branch_id = merchant_id", tableName)).Error
			if err != nil {
				log.Printf("Warning: Could not copy merchant_id to branch_id in %s: %v", tableName, err)
				continue
			}

			// Make branch_id NOT NULL
			err = db.Exec(fmt.Sprintf("ALTER TABLE %s ALTER COLUMN branch_id SET NOT NULL", tableName)).Error
			if err != nil {
				log.Printf("Warning: Could not make branch_id NOT NULL in %s: %v", tableName, err)
				continue
			}

			// Add index for branch_id
			err = db.Exec(fmt.Sprintf("CREATE INDEX IF NOT EXISTS idx_%s_branch_id ON %s(branch_id)", tableName, tableName)).Error
			if err != nil {
				log.Printf("Warning: Could not create branch_id index for %s: %v", tableName, err)
			}

			// Drop foreign key constraint for merchant_id (if exists)
			err = db.Exec(fmt.Sprintf("ALTER TABLE %s DROP CONSTRAINT IF EXISTS fk_%s_merchant", tableName, tableName)).Error
			if err != nil {
				log.Printf("Warning: Could not drop merchant foreign key constraint for %s: %v", tableName, err)
			}

			// Drop merchant_id column
			err = db.Exec(fmt.Sprintf("ALTER TABLE %s DROP COLUMN merchant_id", tableName)).Error
			if err != nil {
				log.Printf("Warning: Could not drop merchant_id column from %s: %v", tableName, err)
				continue
			}

			// Add foreign key constraint for branch_id
			err = db.Exec(fmt.Sprintf("ALTER TABLE %s ADD CONSTRAINT fk_%s_branch FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE", tableName, tableName)).Error
			if err != nil {
				log.Printf("Warning: Could not add branch foreign key constraint for %s: %v", tableName, err)
			}

			log.Printf("Successfully migrated %s to use branch_id", tableName)
		} else if merchantIdExists && branchIdExists {
			log.Printf("Removing NOT NULL constraint from merchant_id in %s", tableName)
			err = db.Exec(fmt.Sprintf("ALTER TABLE %s ALTER COLUMN merchant_id DROP NOT NULL", tableName)).Error
			if err != nil {
				log.Printf("Warning: Could not remove NOT NULL constraint from merchant_id in %s: %v", tableName, err)
				// Continue anyway, this might not be critical
			}
		} else if branchIdExists {
			log.Printf("Table %s already uses branch_id", tableName)
		}
	}

	log.Println("Schema fixes completed successfully")
	return nil
}

// src/config/tableColumns.tsx
import React from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Calendar,
  FileText,
  Mail,
  Copy,
  AlertCircle,
  CheckCircle2,
  Eye,
  DollarSign,
  Edit,
  Trash,
  MoreHorizontal,
  Link2,
  Clock
} from 'lucide-react';
import { TableColumn } from '@/components/common/PaginatedTable';
import { Customer } from '@/redux/services/customersApi';
import { Vendor, Employee, EmploymentStatus, InvoiceStatus, AssetStatus, BillStatus } from '@/lib/types';
import { format } from 'date-fns';
import { Checkbox } from '@/components/ui/checkbox';
import { FormattedCurrency, DateTime } from '@/components/common';
import { useFormatters } from '@/hooks/useFormatters';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
// Import integration badges component
import { ExpenseItemIntegrationBadges } from '@/app/[locale]/expenses/_components/ExpenseItemIntegrationBadges';

// Customer table columns configuration
export const useCustomerColumns = (
  onEdit: (customer: Customer) => void,
  onDelete: (customer: Customer) => void,
  canEdit: boolean,
  canDelete: boolean
): TableColumn<Customer>[] => {
  const t = useTranslations('customers');

  return [
    {
      key: 'name',
      header: t('table.name'),
      sortable: true,
      render: (value: string) => (
        <span className="font-medium">{value}</span>
      ),
      className: 'font-medium'
    },
    {
      key: 'email',
      header: t('table.email'),
      sortable: true,
      render: (value: string) => value || '-'
    },
    {
      key: 'phone',
      header: t('table.phone'),
      sortable: false,
      render: (value: string) => value || '-'
    },
    {
      key: 'status',
      header: t('table.status'),
      sortable: true,
      render: (value: 'active' | 'inactive') => (
        <span
          className={`px-2 py-1 rounded-full text-xs ${
            value === 'active'
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {t(`status.${value}`)}
        </span>
      )
    },
    {
      key: 'actions',
      header: t('table.actions'),
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, customer: Customer) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t('table.actions')}</DropdownMenuLabel>
            {canEdit && (
              <DropdownMenuItem onClick={() => onEdit(customer)}>
                <Edit className="mr-2 h-4 w-4" />
                {t('actions.edit')}
              </DropdownMenuItem>
            )}
            {canDelete && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => onDelete(customer)}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t('actions.delete')}
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];
};

// Invoice table columns configuration
export const useInvoiceColumns = (
  onView: (invoice: any) => void,
  onEdit: (invoice: any) => void,
  onDelete: (invoice: any) => void,
  onEmail: (invoice: any) => void,
  onDuplicate: (invoice: any) => void,
  onVoid: (invoice: any) => void,
  canEdit: boolean,
  canDelete: boolean,
  selectedIds: string[],
  onSelectionChange: (id: string, checked: boolean) => void,
  onSelectAll: (checked: boolean) => void
): TableColumn<any>[] => {
  const t = useTranslations('invoices');

  const getStatusBadgeClass = (status: InvoiceStatus) => {
    switch (status) {
      case InvoiceStatus.Draft:
        return 'bg-gray-100 text-gray-800';
      case InvoiceStatus.Sent:
        return 'bg-blue-100 text-blue-800';
      case InvoiceStatus.PartiallyPaid:
        return 'bg-yellow-100 text-yellow-800';
      case InvoiceStatus.Paid:
        return 'bg-green-100 text-green-800';
      case InvoiceStatus.Overdue:
        return 'bg-red-100 text-red-800';
      case InvoiceStatus.Void:
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Use the formatters hook for consistent formatting
  const { formatCurrency, formatDate: formatDateFn } = useFormatters('USD');

  // Helper function for date formatting with error handling
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'Invalid date';
      return formatDateFn(date);
    } catch {
      return 'Invalid date';
    }
  };

  return [
    {
      key: 'selection',
      header: (
        <Checkbox
          checked={selectedIds.length > 0}
          onCheckedChange={onSelectAll}
          aria-label="Select all"
        />
      ),
      render: (_, invoice: any) => (
        <Checkbox
          checked={selectedIds.includes(invoice.id)}
          onCheckedChange={(checked) => onSelectionChange(invoice.id, !!checked)}
          aria-label={`Select invoice ${invoice.invoiceNumber}`}
        />
      ),
      className: 'w-[50px]'
    },
    {
      key: 'invoiceNumber',
      header: t('table.invoiceNumber'),
      sortable: true,
      render: (value: string) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'customer',
      header: t('table.customer'),
      sortable: false,
      render: (_, invoice: any) => invoice.customer?.name || 'Unknown'
    },
    {
      key: 'issueDate',
      header: t('table.issueDate'),
      sortable: true,
      render: (value: string) => <DateTime date={value} format="short" fallback="N/A" />
    },
    {
      key: 'dueDate',
      header: t('table.dueDate'),
      sortable: true,
      render: (value: string) => <DateTime date={value} format="short" fallback="N/A" />
    },
    {
      key: 'totalAmount',
      header: t('table.amount'),
      sortable: true,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (value: number) => <FormattedCurrency amount={Number(value)} />
    },
    {
      key: 'status',
      header: t('table.status'),
      sortable: true,
      render: (value: InvoiceStatus) => (
        <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeClass(value)}`}>
          {value}
        </span>
      )
    },
    {
      key: 'actions',
      header: t('table.actions'),
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, invoice: any) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onView(invoice)}>
              <FileText className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            {canEdit && invoice.status === InvoiceStatus.Draft && (
              <DropdownMenuItem onClick={() => onEdit(invoice)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
            )}
            {canEdit && invoice.status === InvoiceStatus.Draft && (
              <DropdownMenuItem onClick={() => onEmail(invoice)}>
                <Mail className="mr-2 h-4 w-4" />
                Send Email
              </DropdownMenuItem>
            )}
            {canEdit && (
              <DropdownMenuItem onClick={() => onDuplicate(invoice)}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
            )}
            {canEdit && invoice.status !== InvoiceStatus.Void && (
              <DropdownMenuItem onClick={() => onVoid(invoice)}>
                <AlertCircle className="mr-2 h-4 w-4" />
                Void Invoice
              </DropdownMenuItem>
            )}
            {canDelete && invoice.status === InvoiceStatus.Draft && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDelete(invoice)}
                  className="text-red-600"
                >
                  <Trash className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];
};

// Vendor table columns configuration
export const useVendorColumns = (
  onEdit: (vendor: Vendor) => void,
  onDelete: (vendor: Vendor) => void,
  canEdit: boolean,
  canDelete: boolean
): TableColumn<Vendor>[] => {
  const t = useTranslations('vendors');

  return [
    {
      key: 'name',
      header: t('table.name'),
      sortable: true,
      render: (value: string, vendor: Vendor) => (
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center text-muted-foreground font-medium">
            {value.charAt(0).toUpperCase()}
          </div>
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    {
      key: 'contact_person',
      header: t('table.contactPerson'),
      sortable: true,
      render: (value: string) => value || t('table.na')
    },
    {
      key: 'email',
      header: t('table.email'),
      sortable: true
    },
    {
      key: 'phone',
      header: t('table.phone'),
      sortable: false,
      render: (value: string) => value || t('table.na')
    },
    {
      key: 'address',
      header: t('table.address'),
      sortable: false,
      render: (value: string) => value || t('table.na')
    },
    {
      key: 'actions',
      header: t('table.actions'),
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, vendor: Vendor) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t('table.actions')}</DropdownMenuLabel>
            {canEdit && (
              <DropdownMenuItem onClick={() => onEdit(vendor)}>
                <Edit className="mr-2 h-4 w-4" />
                {t('actions.edit')}
              </DropdownMenuItem>
            )}
            {canDelete && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => onDelete(vendor)}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t('actions.delete')}
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];
};

// Employee table columns configuration
export const useEmployeeColumns = (
  onEdit: (employee: Employee) => void,
  onDeactivate: (employee: Employee) => void,
  canEdit: boolean,
  canDeactivate: boolean
): TableColumn<Employee>[] => {
  const t = useTranslations('employees');
  const common = useTranslations('common');

  return [
    {
      key: 'employee_name',
      header: t('table.name'),
      sortable: true,
      render: (value: string) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'employment_status',
      header: t('table.status'),
      sortable: true,
      render: (value: EmploymentStatus) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          value === EmploymentStatus.Active
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'contact_info',
      header: t('table.contact'),
      sortable: false
    },
    {
      key: 'basic_pay_rate',
      header: 'Pay Rate',
      sortable: true,
      render: (value: number | null | undefined) =>
        value !== undefined && value !== null ? `${value.toFixed(2)}` : 'N/A'
    },
    {
      key: 'pay_frequency',
      header: 'Pay Frequency',
      sortable: true,
      render: (value: string | null) => value ?? 'N/A'
    },
    {
      key: 'actions',
      header: t('table.actions'),
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, employee: Employee) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{common('actions')}</DropdownMenuLabel>
            {canEdit && (
              <DropdownMenuItem onClick={() => onEdit(employee)}>
                {common('edit')}
              </DropdownMenuItem>
            )}
            {canDeactivate && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDeactivate(employee)}
                  className="text-red-600 focus:text-red-700 focus:bg-red-50"
                >
                  Deactivate
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];
};

// Bank accounts table columns
export const useBankAccountColumns = (
  onEdit: (account: any) => void,
  onDelete: (accountId: string) => void,
  onUpload: (account: any) => void
): TableColumn[] => {
  const t = useTranslations('banking');
  const common = useTranslations('common');

  return [
    {
      key: 'account_name',
      header: t('bankAccounts.accountName'),
      sortable: true,
      render: (value: string) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'bank_name',
      header: t('bankAccounts.bankName'),
      sortable: true
    },
    {
      key: 'account_number_masked',
      header: t('bankAccounts.accountNumber'),
      sortable: false
    },
    {
      key: 'currency',
      header: t('bankAccounts.currency'),
      sortable: true
    },
    {
      key: 'linked_coa_account_id',
      header: t('bankAccounts.linkAccount'),
      sortable: false,
      render: (value: string) => value || common('na')
    },
    {
      key: 'is_active',
      header: t('bankAccounts.status'),
      sortable: true,
      render: (value: boolean) => (
        <span
          className={`px-2 py-1 rounded-full text-xs ${
            value
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {value ? t('bankAccounts.statuses.active') : t('bankAccounts.statuses.inactive')}
        </span>
      )
    },
    {
      key: 'actions',
      header: t('bankAccounts.actions'),
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, account: any) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t('bankAccounts.actions')}</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onEdit(account)}>
              {t('bankAccounts.edit')}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onUpload(account)}>
              {t('bankAccounts.import')}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDelete(account.id)}
              className="text-red-600 focus:text-red-600 focus:bg-red-50"
            >
              {t('bankAccounts.delete')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];
};

// Asset table columns configuration
export const useAssetColumns = (
  onEdit: (asset: any) => void,
  onDelete: (asset: any) => void,
  onView: (asset: any) => void,
  onDispose: (asset: any) => void,
  canEdit: boolean,
  canDelete: boolean
): TableColumn<any>[] => {
  const t = useTranslations('assets');
  const { formatCurrency, formatDate } = useFormatters();

  // Calculate book value helper function
  const calculateBookValue = (asset: any) => {
    const purchaseCost = asset.purchase_cost;
    const salvageValue = asset.salvage_value || 0;
    const usefulLifeMonths = asset.useful_life_months || 1;

    // Calculate months since purchase
    const purchaseDate = new Date(asset.purchase_date);
    const today = new Date();
    const monthsSincePurchase = (today.getFullYear() - purchaseDate.getFullYear()) * 12 +
                               (today.getMonth() - purchaseDate.getMonth());

    // Calculate straight-line depreciation
    const depreciableAmount = purchaseCost - salvageValue;
    const monthlyDepreciation = depreciableAmount / usefulLifeMonths;
    const accumulatedDepreciation = Math.min(depreciableAmount, monthlyDepreciation * monthsSincePurchase);

    // Calculate book value
    return Math.max(purchaseCost - accumulatedDepreciation, salvageValue);
  };

  // Get status badge class
  const getStatusBadgeClass = (status: AssetStatus) => {
    switch (status) {
      case AssetStatus.Active:
        return 'bg-green-100 text-green-800';
      case AssetStatus.Disposed:
        return 'bg-gray-100 text-gray-800';
      case AssetStatus.Sold:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return [
    {
      key: 'name',
      header: t('table.name'),
      sortable: true,
      render: (value: string) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'description',
      header: t('table.description'),
      sortable: false,
      render: (value: string) => (
        <span className="max-w-xs truncate">{value || '-'}</span>
      )
    },
    {
      key: 'purchase_date',
      header: t('table.purchaseDate'),
      sortable: true,
      render: (value: string) => <DateTime date={value} format="short" fallback="N/A" />
    },
    {
      key: 'purchase_cost',
      header: t('table.purchaseCost'),
      sortable: true,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (value: number) => <FormattedCurrency amount={Number(value)} />
    },
    {
      key: 'current_value',
      header: t('table.currentValue'),
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, asset: any) => <FormattedCurrency amount={calculateBookValue(asset)} />
    },
    {
      key: 'status',
      header: t('table.status'),
      sortable: true,
      render: (value: AssetStatus) => (
        <Badge className={getStatusBadgeClass(value)}>
          {t(`status.${value.toLowerCase()}`)}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: t('table.actions'),
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, asset: any) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t('table.actions')}</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onView(asset)}>
              <Eye className="mr-2 h-4 w-4" />
              {t('actions.view')}
            </DropdownMenuItem>
            {canEdit && (
              <DropdownMenuItem onClick={() => onEdit(asset)}>
                <Edit className="mr-2 h-4 w-4" />
                {t('actions.edit')}
              </DropdownMenuItem>
            )}
            {asset.status === AssetStatus.Active && (
              <DropdownMenuItem onClick={() => onDispose(asset)}>
                <DollarSign className="mr-2 h-4 w-4" />
                {t('actions.dispose')}
              </DropdownMenuItem>
            )}
            {canDelete && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => onDelete(asset)}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t('actions.delete')}
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];
};

// Bill table columns configuration
export const useBillColumns = (
  onEdit: (bill: any) => void,
  onDelete: (bill: any) => void,
  onView: (bill: any) => void,
  onPay: (bill: any) => void,
  getVendorName: (vendorId?: string) => string,
  canEdit: boolean,
  canDelete: boolean
): TableColumn<any>[] => {
  const t = useTranslations('bills');
  const { formatCurrency, formatDate } = useFormatters();

  // Get status badge class
  const getStatusBadgeClass = (status: BillStatus) => {
    switch (status) {
      case BillStatus.Draft:
        return 'bg-gray-100 text-gray-800';
      case BillStatus.Pending:
        return 'bg-yellow-100 text-yellow-800';
      case BillStatus.PartiallyPaid:
        return 'bg-blue-100 text-blue-800';
      case BillStatus.Paid:
        return 'bg-green-100 text-green-800';
      case BillStatus.Overdue:
        return 'bg-red-100 text-red-800';
      case BillStatus.Void:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return [
    {
      key: 'billNumber',
      header: t('table.billNumber'),
      sortable: true,
      render: (value: string) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'vendor_id',
      header: t('table.vendor'),
      sortable: false,
      render: (value: string) => getVendorName(value)
    },
    {
      key: 'billDate',
      header: t('table.billDate'),
      sortable: true,
      render: (value: string) => <DateTime date={value} format="short" fallback="N/A" />
    },
    {
      key: 'dueDate',
      header: t('table.dueDate'),
      sortable: true,
      render: (value: string) => <DateTime date={value} format="short" fallback="N/A" />
    },
    {
      key: 'totalAmount',
      header: t('table.totalAmount'),
      sortable: true,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (value: number) => <FormattedCurrency amount={Number(value)} />
    },
    {
      key: 'status',
      header: t('table.status'),
      sortable: true,
      render: (value: BillStatus) => (
        <Badge className={getStatusBadgeClass(value)}>
          {t(`status.${value.toLowerCase()}`)}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: t('table.actions'),
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, bill: any) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t('table.actions')}</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onView(bill)}>
              <Eye className="mr-2 h-4 w-4" />
              {t('actions.view')}
            </DropdownMenuItem>
            {canEdit && (
              <DropdownMenuItem onClick={() => onEdit(bill)}>
                <Edit className="mr-2 h-4 w-4" />
                {t('actions.edit')}
              </DropdownMenuItem>
            )}
            {(bill.status === BillStatus.Pending || bill.status === BillStatus.PartiallyPaid) && (
              <DropdownMenuItem onClick={() => onPay(bill)}>
                <DollarSign className="mr-2 h-4 w-4" />
                {t('actions.pay')}
              </DropdownMenuItem>
            )}
            {canDelete && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => onDelete(bill)}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t('actions.delete')}
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];
};

// Chart of Accounts table columns configuration
export const useChartOfAccountsColumns = (
  onEdit: (account: any) => void,
  onDelete: (account: any) => void,
  onBankLink: (account: any) => void,
  canEdit: boolean,
  canDelete: boolean
): TableColumn<any>[] => {
  const t = useTranslations('common');

  // Get account type badge variant
  const getAccountTypeBadgeVariant = (accountType: string) => {
    if (accountType?.startsWith('Asset')) return 'default';
    if (accountType?.startsWith('Liability')) return 'destructive';
    if (accountType?.startsWith('Equity')) return 'secondary';
    return 'outline';
  };

  return [
    {
      key: 'account_code',
      header: t('code'),
      sortable: true,
      render: (value: string, account: any) => (
        <span className="font-medium">
          {(account as any).code || account.account_code}
        </span>
      )
    },
    {
      key: 'account_name',
      header: t('name'),
      sortable: true,
      render: (value: string, account: any) => (
        <span>{(account as any).name || account.account_name}</span>
      )
    },
    {
      key: 'account_type',
      header: t('type'),
      sortable: false,
      render: (value: string, account: any) => {
        const accountType = (account as any).type || account.account_type;
        return (
          <Badge variant={getAccountTypeBadgeVariant(accountType)}>
            {accountType}
          </Badge>
        );
      }
    },
    {
      key: 'description',
      header: t('description'),
      sortable: false,
      render: (value: string) => (
        <span className="max-w-xs truncate">{value || '-'}</span>
      )
    },
    {
      key: 'is_active',
      header: t('status'),
      sortable: true,
      render: (value: boolean) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? t('active') : t('inactive')}
        </Badge>
      )
    },
    {
      key: 'banking',
      header: 'Banking',
      sortable: false,
      render: (_, account: any) => (
        <span className="text-sm text-muted-foreground">
          {/* Banking status will be handled by BankAccountStatus component */}
          -
        </span>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, account: any) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            {canEdit && (
              <DropdownMenuItem onClick={() => onEdit(account)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={() => onBankLink(account)}>
              <Link2 className="mr-2 h-4 w-4" />
              Link Bank
            </DropdownMenuItem>
            {canDelete && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => onDelete(account)}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];
};

// Payroll Runs table columns configuration
export const usePayrollRunColumns = (
  onView: (run: any) => void,
  onEdit: (run: any) => void,
  onDelete: (run: any) => void,
  onProcess: (run: any) => void,
  onCancel: (runId: string) => void,
  canEdit: boolean,
  canDelete: boolean
): TableColumn<any>[] => {
  const { formatCurrency, formatDate } = useFormatters();

  // Get status badge variant and icon
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'Draft': return 'secondary';
      case 'Processed': return 'default';
      case 'Cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Draft': return <Clock className="h-3 w-3" />;
      case 'Processed': return <CheckCircle2 className="h-3 w-3" />;
      case 'Cancelled': return <AlertCircle className="h-3 w-3" />;
      default: return <AlertCircle className="h-3 w-3" />;
    }
  };

  return [
    {
      key: 'pay_period',
      header: 'Pay Period',
      sortable: true,
      render: (_, run: any) => (
        <div>
          <div className="font-medium">
            {formatDate(run.pay_period_start_date)} - {formatDate(run.pay_period_end_date)}
          </div>
          <div className="text-xs text-muted-foreground">
            ID: {run.payroll_run_id}
          </div>
        </div>
      )
    },
    {
      key: 'payment_date',
      header: 'Payment Date',
      sortable: true,
      render: (value: string) => formatDate(value)
    },
    {
      key: 'gross_wages',
      header: 'Gross Wages',
      sortable: true,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (value: number) => (
        <span className="font-medium text-green-600">
          {formatCurrency(value)}
        </span>
      )
    },
    {
      key: 'employee_taxes',
      header: 'Employee Taxes',
      sortable: true,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (value: number) => (
        <span className="font-medium text-orange-600">
          {formatCurrency(value)}
        </span>
      )
    },
    {
      key: 'net_pay',
      header: 'Net Pay',
      sortable: true,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (value: number) => (
        <span className="font-medium text-blue-600">
          {formatCurrency(value)}
        </span>
      )
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      render: (value: string) => (
        <Badge variant={getStatusBadgeVariant(value)} className="flex items-center gap-1 w-fit">
          {getStatusIcon(value)}
          {value}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, run: any) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => onView(run)}>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>

            {run.status === 'Draft' && (
              <>
                {canEdit && (
                  <DropdownMenuItem onClick={() => onEdit(run)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={() => onProcess(run)}>
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  Process
                </DropdownMenuItem>
                {canDelete && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-red-600"
                      onClick={() => onDelete(run)}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </>
                )}
              </>
            )}

            {run.status === 'Processed' && (
              <DropdownMenuItem onClick={() => onCancel(run.payroll_run_id)}>
                <AlertCircle className="mr-2 h-4 w-4" />
                Cancel
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];
};

// Users table columns configuration
export const useUserColumns = (
  onEdit: (user: any) => void,
  onDelete: (user: any) => void,
  canEdit: boolean,
  canDelete: boolean
): TableColumn<any>[] => {
  const { formatDate } = useFormatters();

  // Get role badge variant
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin': return 'default';
      case 'finance': return 'secondary';
      case 'staff': return 'outline';
      default: return 'outline';
    }
  };

  // Get role badge color classes
  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-blue-100 text-blue-800';
      case 'finance': return 'bg-purple-100 text-purple-800';
      case 'staff': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return [
    {
      key: 'name',
      header: 'Name',
      sortable: true,
      render: (value: string) => (
        <span className="font-medium">{value}</span>
      )
    },
    {
      key: 'email',
      header: 'Email',
      sortable: true,
      render: (value: string) => value
    },
    {
      key: 'role',
      header: 'Role',
      sortable: true,
      render: (value: string) => (
        <span className={`px-2 py-1 rounded-full text-xs ${getRoleBadgeColor(value || 'staff')}`}>
          {value ? value.charAt(0).toUpperCase() + value.slice(1) : 'Staff'}
        </span>
      )
    },
    {
      key: 'isActive',
      header: 'Status',
      sortable: false,
      render: (value: boolean) => (
        <span className={`px-2 py-1 rounded-full text-xs ${
          value !== false
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {value !== false ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'merchants',
      header: 'Merchants',
      sortable: false,
      render: (value: any[], user: any) => {
        if (!value || value.length === 0) {
          return <span className="text-muted-foreground text-xs">None</span>;
        }

        if (value.length <= 2) {
          return (
            <div className="flex flex-wrap gap-1">
              {value.map(merchant => (
                <Badge key={merchant.id} variant="outline" className="text-xs">
                  {merchant.name}
                  {user.defaultMerchantId === merchant.id && (
                    <span className="ml-1 text-green-600">★</span>
                  )}
                </Badge>
              ))}
            </div>
          );
        }

        return (
          <div className="flex flex-wrap gap-1">
            <Badge variant="outline" className="text-xs">
              {value[0].name}
              {user.defaultMerchantId === value[0].id && (
                <span className="ml-1 text-green-600">★</span>
              )}
            </Badge>
            <Badge variant="outline" className="text-xs">
              +{value.length - 1} more
            </Badge>
          </div>
        );
      }
    },
    {
      key: 'createdAt',
      header: 'Created At',
      sortable: true,
      render: (value: string) =>
        value ? formatDate(value) : 'N/A'
    },
    {
      key: 'actions',
      header: 'Actions',
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, user: any) => (
        <div className="flex justify-end gap-1">
          {canEdit && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onEdit(user)}
              title="Edit user"
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {canDelete && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDelete(user)}
              title="Delete user"
            >
              <Trash className="h-4 w-4" />
            </Button>
          )}
        </div>
      )
    }
  ];
};

// Expenses table columns configuration
export const useExpenseColumns = (
  onEdit: (expense: any) => void,
  onDelete: (expense: any) => void,
  getVendorName: (vendorId: string) => string,
  canEdit: boolean,
  canDelete: boolean,
  onManageIntegrations?: (expense: any) => void
): TableColumn<any>[] => {
  const t = useTranslations('expenses');
  const { formatCurrency } = useFormatters();

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'bg-green-100 text-green-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      case 'PAID': return 'bg-blue-100 text-blue-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  // Check if expense can be deleted (only pending expenses)
  const canDeleteExpense = (expense: any) => {
    return canDelete && expense.status === 'PENDING';
  };

  return [
    {
      key: 'date',
      header: t('table.date'),
      sortable: true,
      render: (value: string) => <DateTime date={value} format="short" fallback="N/A" />
    },
    {
      key: 'vendor_id',
      header: t('table.vendor'),
      sortable: false,
      render: (value: string, expense: any) =>
        expense.vendor_name || getVendorName(value)
    },
    {
      key: 'category',
      header: t('table.category'),
      sortable: true,
      render: (value: string) => value
    },
    {
      key: 'description',
      header: t('table.description'),
      sortable: false,
      render: (value: string) => (
        <span className="max-w-xs truncate">{value}</span>
      )
    },
    {
      key: 'amount',
      header: t('table.amount'),
      sortable: true,
      headerClassName: 'text-right',
      className: 'text-right font-medium',
      render: (value: number) => <FormattedCurrency amount={Number(value)} />
    },
    {
      key: 'payment_method',
      header: t('table.paymentMethod'),
      sortable: false,
      render: (value: string) => value
    },
    {
      key: 'status',
      header: t('table.status'),
      sortable: true,
      render: (value: string) => (
        <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeColor(value)}`}>
          {value.charAt(0) + value.slice(1).toLowerCase()}
        </span>
      )
    },
    // ✅ Integration column - only show if onManageIntegrations is provided
    ...(onManageIntegrations ? [{
      key: 'integrations',
      header: 'Integrations',
      sortable: false,
      className: 'w-[120px]',
      render: (_: any, expense: any) => (
        <ExpenseItemIntegrationBadges
          expenseId={expense.id}
          onManageIntegrations={() => onManageIntegrations(expense)}
          compact={true}
        />
      )
    }] : []),
    {
      key: 'actions',
      header: t('table.actions'),
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, expense: any) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t('table.actions')}</DropdownMenuLabel>
            {canEdit && (
              <DropdownMenuItem onClick={() => onEdit(expense)}>
                <Edit className="mr-2 h-4 w-4" />
                {t('actions.edit')}
              </DropdownMenuItem>
            )}
            {canDeleteExpense(expense) && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => onDelete(expense)}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t('actions.delete')}
                </DropdownMenuItem>
              </>
            )}
            {canDelete && !canDeleteExpense(expense) && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-gray-400 cursor-not-allowed"
                  disabled
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t('actions.delete')} (Only pending)
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )
    }
  ];
};

// Journal Entries table columns configuration
export const useJournalEntryColumns = (
  onViewDetails: (entry: any) => void
): TableColumn<any>[] => {
  const t = useTranslations('journalEntries');

  return [
    {
      key: 'entry_date',
      header: t('table.entryDate'),
      sortable: true,
      render: (value: string, entry: any) =>
        <DateTime date={value || entry.date} format="short" fallback="N/A" />
    },
    {
      key: 'description',
      header: t('table.description'),
      sortable: true,
      render: (value: string) => (
        <span className="max-w-xs truncate">{value}</span>
      )
    },
    {
      key: 'source_type',
      header: t('table.sourceType'),
      sortable: true,
      render: (value: string, entry: any) =>
        value || entry.sourceType || 'N/A'
    },
    {
      key: 'reference',
      header: t('table.reference'),
      sortable: false,
      render: (value: string, entry: any) =>
        value || entry.source_id || 'N/A'
    },
    {
      key: 'actions',
      header: t('table.actions'),
      sortable: false,
      headerClassName: 'text-right',
      className: 'text-right',
      render: (_, entry: any) => (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onViewDetails(entry)}
        >
          {t('actions.viewDetails')}
        </Button>
      )
    }
  ];
};

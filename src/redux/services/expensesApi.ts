import { createApi } from '@reduxjs/toolkit/query/react';
import { authenticatedBaseQuery } from '../baseQuery';

export interface Expense {
  id: string;
  date: string;
  vendor_id: string;
  vendor_name?: string;
  category: string;
  description: string;
  amount: number;
  payment_method: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID';
  receipt_url?: string;
  created_at: string;
  updated_at: string;

  // Enhanced fields for better expense tracking
  bill_id?: string;
  asset_id?: string;
  asset_name?: string;
  employee_id?: string;
  employee_name?: string;
  purchase_order_id?: string;
  project_id?: string;
  cost_center?: string;
  department?: string;
  expense_category_id?: string;
  category_name?: string;
  bank_account_id?: string;
  bank_account_name?: string;
  approved_by?: string;
  approved_by_name?: string;
  approved_at?: string;
  paid_at?: string;
  sub_total: number;
  tax_amount: number;
  discount_amount: number;
  created_by?: string;
  created_by_name?: string;
  is_recurring: boolean;
}

export interface CreateExpenseRequest {
  date: string;
  vendor_id: string;
  category: string;
  description: string;
  amount: number;
  payment_method: string;
  receipt_url?: string;
  branch_id?: string; // Added for backend compatibility
  account_id?: string; // Added for backend compatibility
}

export interface UpdateExpenseRequest {
  id: string;
  body: Partial<Expense>;
}

// Define query parameters for expenses list
export interface ExpensesQueryParams {
  search?: string;
  status?: string;
  category?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// API response format
export interface ExpensesResponse {
  data: Expense[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const expensesApi = createApi({
  reducerPath: 'expensesApi',
  baseQuery: authenticatedBaseQuery,
  tagTypes: ['Expense'],
  endpoints: (builder) => ({
    getExpenses: builder.query<ExpensesResponse, ExpensesQueryParams | void>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();

        if (params.search) searchParams.append('search', params.search);
        if (params.status) searchParams.append('status', params.status);
        if (params.category) searchParams.append('category', params.category);
        if (params.page) searchParams.append('page', params.page.toString());
        if (params.limit) searchParams.append('limit', params.limit.toString());
        if (params.sortBy) searchParams.append('sortBy', params.sortBy);
        if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder);

        const queryString = searchParams.toString();
        return `/expenses${queryString ? `?${queryString}` : ''}`;
      },
      providesTags: (result) =>
        result?.data
          ? [
              ...result.data.map(({ id }) => ({ type: 'Expense' as const, id })),
              { type: 'Expense', id: 'LIST' },
            ]
          : [{ type: 'Expense', id: 'LIST' }],
    }),
    getExpenseById: builder.query<Expense, string>({
      query: (id) => `/expenses/${id}`,
      providesTags: (result, error, id) => [{ type: 'Expense', id }],
    }),
    createExpense: builder.mutation<Expense, CreateExpenseRequest>({
      query: (body) => ({
        url: '/expenses',
        method: 'POST',
        body,
      }),
      invalidatesTags: [{ type: 'Expense', id: 'LIST' }],
    }),
    updateExpense: builder.mutation<Expense, UpdateExpenseRequest>({
      query: ({ id, body }) => ({
        url: `/expenses/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Expense', id },
        { type: 'Expense', id: 'LIST' },
      ],
    }),
    deleteExpense: builder.mutation<void, string>({
      query: (id) => ({
        url: `/expenses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [{ type: 'Expense', id: 'LIST' }],
    }),
  }),
});

export const {
  useGetExpensesQuery,
  useGetExpenseByIdQuery,
  useCreateExpenseMutation,
  useUpdateExpenseMutation,
  useDeleteExpenseMutation,
} = expensesApi;

import { createApi } from '@reduxjs/toolkit/query/react';
import { BudgetItem, BudgetReport, BudgetTemplate } from '@/lib/types';
import { branchAwareBaseQuery } from '../baseQuery';

// Budget Integration Types
export interface BudgetIntegrationSettings {
  id: string;
  branchId: string;
  integrationType: 'inventory' | 'customer' | 'vendor' | 'tax' | 'banking';
  enabled: boolean;
  threshold?: number;
  frequency?: 'daily' | 'weekly' | 'monthly';
  autoApproval?: boolean;
  notifications?: boolean;
  reorderPoint?: number;
  forecastPeriod?: 'quarterly' | 'annual' | 'bi-annual';
  reconciliationMode?: 'automatic' | 'manual' | 'hybrid';
  taxCalculation?: 'accrual' | 'cash' | 'hybrid';
  cashFlowPrediction?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface BudgetItemIntegration {
  id: string;
  budgetItemId: string;
  inventoryItemId?: string;
  customerId?: string;
  vendorId?: string;
  projectId?: string;
  employeeId?: string;
  assetId?: string;
  bankAccountId?: string;
  taxCategoryId?: string;
  integrationType: string;
  metadata?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BudgetForecast {
  id: string;
  branchId: string;
  accountId: string;
  integrationType: string;
  forecastPeriod: string;
  year: number;
  quarter?: number;
  predictedAmount: number;
  confidenceLevel: number;
  basedOnHistoricalData: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface BudgetAlert {
  id: string;
  branchId: string;
  budgetItemId?: string;
  integrationType: string;
  alertType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  currentValue?: number;
  thresholdValue?: number;
  isRead: boolean;
  isResolved: boolean;
  resolvedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Response interface for budget items from Rust backend
interface BudgetItemsResponse {
  items: BudgetItem[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    total_pages: number;
    has_next_page: boolean;
    has_previous_page: boolean;
  };
}

export const budgetApi = createApi({
  reducerPath: 'budgetApi',
  baseQuery: branchAwareBaseQuery,
  tagTypes: ['BudgetItem', 'BudgetReport', 'BudgetTemplate', 'BudgetIntegrationSettings', 'BudgetAlert', 'BudgetForecast'],
  endpoints: (builder) => ({
    // Budget Items
    getBudgetItems: builder.query<BudgetItem[], { year?: number; month?: number; branchId?: string; organizationId?: string }>({
      query: ({ year, month, branchId, organizationId }) => {
        let url = 'budget/items';
        const params = new URLSearchParams();
        if (year) params.append('year', year.toString());
        if (month) params.append('month', month.toString());
        if (branchId) params.append('branchId', branchId);
        if (organizationId) params.append('organizationId', organizationId);
        const queryString = params.toString();
        return queryString ? `${url}?${queryString}` : url;
      },
      transformResponse: (response: BudgetItemsResponse | BudgetItem[]) => {
        // Handle both response formats for backward compatibility
        if (Array.isArray(response)) {
          return response;
        }
        return response.data || response.items || [];
      },
      providesTags: ['BudgetItem'],
    }),
    getBudgetItemById: builder.query<BudgetItem, string>({
      query: (id) => `budget/items/${id}`,
      providesTags: (result, error, id) => [{ type: 'BudgetItem', id }],
    }),
    createBudgetItem: builder.mutation<BudgetItem, Partial<BudgetItem>>({
      query: (body) => ({
        url: 'budget/items',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['BudgetItem'],
    }),
    updateBudgetItem: builder.mutation<BudgetItem, { id: string; body: Partial<BudgetItem> }>({
      query: ({ id, body }) => ({
        url: `budget/items/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'BudgetItem', id }],
    }),
    deleteBudgetItem: builder.mutation<void, string>({
      query: (id) => ({
        url: `budget/items/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['BudgetItem'],
    }),

    // Budget Reports
    getBudgetReport: builder.query<BudgetReport, { year: number; month?: number; branchId?: string; organizationId?: string }>({
      query: ({ year, month, branchId, organizationId }) => {
        let url = 'budget/reports';
        const params = new URLSearchParams();
        params.append('year', year.toString());
        if (month) params.append('month', month.toString());
        if (branchId) params.append('branchId', branchId);
        if (organizationId) params.append('organizationId', organizationId);
        return `${url}?${params.toString()}`;
      },
      providesTags: ['BudgetReport'],
    }),

    // Budget Export
    exportBudget: builder.query<void, { year: number; month?: number; format?: string }>({
      query: ({ year, month, format = 'csv' }) => {
        let url = 'budget/export';
        const params = new URLSearchParams();
        params.append('year', year.toString());
        if (month) params.append('month', month.toString());
        params.append('format', format);
        return `${url}?${params.toString()}`;
      },
    }),

    // Budget Import
    importBudget: builder.mutation<
      { message: string; results: { created: number; updated: number; skipped: number; errors: any[] } },
      any[]
    >({
      query: (body) => ({
        url: 'budget/import',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['BudgetItem', 'BudgetReport'],
    }),

    // Budget Templates
    getBudgetTemplates: builder.query<BudgetTemplate[], { branchId?: string; organizationId?: string } | void>({
      query: (params = {}) => {
        let url = 'budget/templates';
        const queryParams = new URLSearchParams();
        if (params.branchId) queryParams.append('branchId', params.branchId);
        if (params.organizationId) queryParams.append('organizationId', params.organizationId);
        const queryString = queryParams.toString();
        return queryString ? `${url}?${queryString}` : url;
      },
      providesTags: ['BudgetTemplate'],
    }),

    getBudgetTemplateById: builder.query<BudgetTemplate, string>({
      query: (id) => `budget/templates/${id}`,
      providesTags: (result, error, id) => [{ type: 'BudgetTemplate', id }],
    }),

    createBudgetTemplate: builder.mutation<
      BudgetTemplate,
      {
        name: string;
        description?: string;
        isDefault?: boolean;
        branchId: string;
        items: Array<{
          accountId: string;
          amount: number;
          notes?: string;
        }>;
      }
    >({
      query: (body) => ({
        url: 'budget/templates',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['BudgetTemplate'],
    }),

    updateBudgetTemplate: builder.mutation<
      BudgetTemplate,
      {
        id: string;
        body: {
          name?: string;
          description?: string;
          isDefault?: boolean;
          items?: Array<{
            id?: string;
            accountId: string;
            amount: number;
            notes?: string;
          }>;
        };
      }
    >({
      query: ({ id, body }) => ({
        url: `budget/templates/${id}`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'BudgetTemplate', id },
        'BudgetTemplate',
      ],
    }),

    deleteBudgetTemplate: builder.mutation<void, string>({
      query: (id) => ({
        url: `budget/templates/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['BudgetTemplate'],
    }),

    applyBudgetTemplate: builder.mutation<
      { message: string; results: { created: number; updated: number; skipped: number; errors: any[] } },
      { id: string; year: number; month: number }
    >({
      query: ({ id, year, month }) => ({
        url: `budget/templates/${id}/apply`,
        method: 'POST',
        body: { year, month },
      }),
      invalidatesTags: ['BudgetItem', 'BudgetReport'],
    }),

    // Export Budget Template
    exportBudgetTemplate: builder.query<void, { id: string; format?: string }>({
      query: ({ id, format = 'csv' }) => {
        const params = new URLSearchParams();
        params.append('format', format);
        return `budget/templates/${id}/export?${params.toString()}`;
      },
    }),

    // Duplicate Budget Template
    duplicateBudgetTemplate: builder.mutation<BudgetTemplate, { id: string; name: string }>({
      query: ({ id, name }) => ({
        url: `budget/templates/${id}/duplicate`,
        method: 'POST',
        body: { name },
      }),
      invalidatesTags: ['BudgetTemplate'],
    }),

    // Bulk operations for budget items
    bulkDeleteBudgetItems: builder.mutation<
      { message: string; deletedCount: number },
      string[]
    >({
      query: (ids) => ({
        url: 'budget/items/bulk-delete',
        method: 'DELETE',
        body: { ids },
      }),
      invalidatesTags: ['BudgetItem', 'BudgetReport'],
    }),

    bulkUpdateBudgetItems: builder.mutation<
      { message: string; updatedCount: number },
      { ids: string[]; updates: Partial<BudgetItem> }
    >({
      query: ({ ids, updates }) => ({
        url: 'budget/items/bulk-update',
        method: 'PUT',
        body: { ids, updates },
      }),
      invalidatesTags: ['BudgetItem', 'BudgetReport'],
    }),

    // Export selected budget items
    exportBudgetItems: builder.mutation<
      Blob,
      { ids?: string[]; format: 'csv' | 'excel' | 'pdf'; year?: number; month?: number }
    >({
      query: ({ ids, format, year, month }) => ({
        url: 'budget/items/export',
        method: 'POST',
        body: { ids, format, year, month },
        responseHandler: (response) => response.blob(),
      }),
    }),

    // Get budget comparison between periods
    getBudgetComparison: builder.query<
      {
        currentPeriod: { year: number; month: number; total: number };
        previousPeriod: { year: number; month: number; total: number };
        variance: number;
        variancePercent: number;
        items: Array<{
          accountId: string;
          accountName: string;
          current: number;
          previous: number;
          variance: number;
          variancePercent: number;
        }>;
      },
      { currentYear: number; currentMonth: number; previousYear: number; previousMonth: number; branchId?: string; organizationId?: string }
    >({
      query: ({ currentYear, currentMonth, previousYear, previousMonth, branchId, organizationId }) => {
        const params = new URLSearchParams();
        params.append('currentYear', currentYear.toString());
        params.append('currentMonth', currentMonth.toString());
        params.append('previousYear', previousYear.toString());
        params.append('previousMonth', previousMonth.toString());
        if (branchId) params.append('branchId', branchId);
        if (organizationId) params.append('organizationId', organizationId);
        return `budget/comparison?${params.toString()}`;
      },
      providesTags: ['BudgetReport'],
    }),

    // Budget Integration Settings
    getBudgetIntegrationSettings: builder.query<{ settings: BudgetIntegrationSettings[] }, { branchId: string }>({
      query: ({ branchId }) => `budget/integrations/settings?branchId=${branchId}`,
      providesTags: ['BudgetIntegrationSettings'],
    }),

    updateBudgetIntegrationSettings: builder.mutation<{ message: string }, { branchId: string; settings: BudgetIntegrationSettings[] }>({
      query: ({ branchId, settings }) => ({
        url: `budget/integrations/settings?branchId=${branchId}`,
        method: 'PUT',
        body: { settings },
      }),
      invalidatesTags: ['BudgetIntegrationSettings'],
    }),

    // Budget Item Integrations
    createBudgetItemIntegration: builder.mutation<{ message: string; integration: BudgetItemIntegration }, BudgetItemIntegration>({
      query: (integration) => ({
        url: 'budget/integrations/item-integrations',
        method: 'POST',
        body: integration,
      }),
      invalidatesTags: ['BudgetItem'],
    }),

    updateBudgetItemIntegration: builder.mutation<{ message: string }, { id: string; updates: Partial<BudgetItemIntegration> }>({
      query: ({ id, updates }) => ({
        url: `budget/integrations/item-integrations/${id}`,
        method: 'PUT',
        body: updates,
      }),
      invalidatesTags: ['BudgetItem'],
    }),

    getBudgetItemIntegrations: builder.query<{ integrations: BudgetItemIntegration[] }, { budgetItemId: string }>({
      query: ({ budgetItemId }) => `budget/integrations/item-integrations/${budgetItemId}`,
      providesTags: ['BudgetItem'],
    }),

    // Budget Forecasts
    generateBudgetForecast: builder.mutation<{ message: string; forecast: BudgetForecast }, {
      branchId: string;
      accountId: string;
      integrationType: string;
      forecastPeriod: string;
      year: number;
    }>({
      query: (params) => ({
        url: 'budget/integrations/forecast',
        method: 'POST',
        body: params,
      }),
      invalidatesTags: ['BudgetForecast'],
    }),

    // Budget Alerts
    getBudgetAlerts: builder.query<{ alerts: BudgetAlert[] }, { branchId: string; unreadOnly?: boolean }>({
      query: ({ branchId, unreadOnly }) => {
        const params = new URLSearchParams({ branchId });
        if (unreadOnly) params.append('unreadOnly', 'true');
        return `budget/integrations/alerts?${params.toString()}`;
      },
      providesTags: ['BudgetAlert'],
    }),

    markBudgetAlertAsRead: builder.mutation<{ message: string }, { alertId: string }>({
      query: ({ alertId }) => ({
        url: `budget/integrations/alerts/${alertId}/read`,
        method: 'PUT',
      }),
      invalidatesTags: ['BudgetAlert'],
    }),

    resolveBudgetAlert: builder.mutation<{ message: string }, { alertId: string }>({
      query: ({ alertId }) => ({
        url: `budget/integrations/alerts/${alertId}/resolve`,
        method: 'PUT',
      }),
      invalidatesTags: ['BudgetAlert'],
    }),

    // Threshold Checking
    checkBudgetThresholds: builder.mutation<{ message: string }, { branchId: string }>({
      query: ({ branchId }) => ({
        url: `budget/integrations/check-thresholds?branchId=${branchId}`,
        method: 'POST',
      }),
      invalidatesTags: ['BudgetAlert'],
    }),

    // Integration-specific data endpoints
    getInventoryBudgetData: builder.query<{ message: string; data: any }, { branchId: string; year: number }>({
      query: ({ branchId, year }) => `budget/integrations/inventory?branchId=${branchId}&year=${year}`,
    }),

    getCustomerBudgetData: builder.query<{ message: string; data: any }, { branchId: string; year: number }>({
      query: ({ branchId, year }) => `budget/integrations/customer?branchId=${branchId}&year=${year}`,
    }),

    getVendorBudgetData: builder.query<{ message: string; data: any }, { branchId: string; year: number }>({
      query: ({ branchId, year }) => `budget/integrations/vendor?branchId=${branchId}&year=${year}`,
    }),

    getTaxBudgetData: builder.query<{ message: string; data: any }, { branchId: string; year: number }>({
      query: ({ branchId, year }) => `budget/integrations/tax?branchId=${branchId}&year=${year}`,
    }),

    getBankingBudgetData: builder.query<{ message: string; data: any }, { branchId: string; year: number }>({
      query: ({ branchId, year }) => `budget/integrations/banking?branchId=${branchId}&year=${year}`,
    }),
  }),
});

export const {
  useGetBudgetItemsQuery,
  useGetBudgetItemByIdQuery,
  useCreateBudgetItemMutation,
  useUpdateBudgetItemMutation,
  useDeleteBudgetItemMutation,
  useGetBudgetReportQuery,
  useExportBudgetQuery,
  useImportBudgetMutation,
  useGetBudgetTemplatesQuery,
  useGetBudgetTemplateByIdQuery,
  useCreateBudgetTemplateMutation,
  useUpdateBudgetTemplateMutation,
  useDeleteBudgetTemplateMutation,
  useApplyBudgetTemplateMutation,
  useExportBudgetTemplateMutation,
  useDuplicateBudgetTemplateMutation,
  useBulkDeleteBudgetItemsMutation,
  useBulkUpdateBudgetItemsMutation,
  useExportBudgetItemsMutation,
  useGetBudgetComparisonQuery,

  // Budget Integration hooks
  useGetBudgetIntegrationSettingsQuery,
  useUpdateBudgetIntegrationSettingsMutation,
  useCreateBudgetItemIntegrationMutation,
  useUpdateBudgetItemIntegrationMutation,
  useGetBudgetItemIntegrationsQuery,
  useGenerateBudgetForecastMutation,
  useGetBudgetAlertsQuery,
  useMarkBudgetAlertAsReadMutation,
  useResolveBudgetAlertMutation,
  useCheckBudgetThresholdsMutation,
  useGetInventoryBudgetDataQuery,
  useGetCustomerBudgetDataQuery,
  useGetVendorBudgetDataQuery,
  useGetTaxBudgetDataQuery,
  useGetBankingBudgetDataQuery,
} = budgetApi;

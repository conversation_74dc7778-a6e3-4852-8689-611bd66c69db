'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Plus, Settings } from 'lucide-react';
import { useTranslations } from 'next-intl';

// Import dialog components from _components folder
import { ExpenseFormDialog } from './_components/ExpenseFormDialog';
import { DeleteExpenseDialog } from './_components/DeleteExpenseDialog';
import { ExpenseIntegrationDialog } from './_components/ExpenseIntegrationDialog';
import { ExpenseItemIntegrationDialog } from './_components/ExpenseItemIntegrationDialog';
import { useFormatters } from '@/hooks/useFormatters';

// ✅ Import advanced pagination components
import { useAdvancedPagination } from '@/hooks/useAdvancedPagination';
import { PaginatedTable } from '@/components/common/PaginatedTable';
import { useExpenseColumns } from '@/config/tableColumns';

// ✅ Import enhanced features
import { ExportData } from '@/components/common/ExportData';
import { BulkActions, createCommonBulkActions } from '@/components/common/BulkActions';

// Import RTK Query hooks
import {
  useGetExpensesQuery,
  useDeleteExpenseMutation,
  Expense,
} from '@/redux/services/expensesApi';
import { useGetVendorsQuery } from '@/redux/services/vendorsApi';
import { useBranchContext } from '@/contexts/BranchContext';

export default function ExpensesPage() {
  const { toast } = useToast();
  const t = useTranslations('expenses');
  const common = useTranslations('common');
  const { selectedBranchId, selectedOrganizationId } = useBranchContext();

  // ✅ Replace manual search state with advanced pagination
  const { state, actions, queryParams } = useAdvancedPagination({
    initialSort: { sortBy: 'date', sortOrder: 'desc' },
    initialLimit: 10,
    persist: true,
    persistKey: 'expenses-page'
  });

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null);

  // ✅ Integration dialog states
  const [isIntegrationDialogOpen, setIsIntegrationDialogOpen] = useState(false);
  const [isItemIntegrationDialogOpen, setIsItemIntegrationDialogOpen] = useState(false);
  const [selectedExpenseForIntegration, setSelectedExpenseForIntegration] = useState<Expense | null>(null);

  // ✅ Enhanced features state
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isExporting, setIsExporting] = useState(false);

  // ✅ Advanced filters state
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});
  const [amountRange, setAmountRange] = useState<{ min?: number; max?: number }>({});

  // Expense categories - moved here to be available before filters configuration
  const expenseCategories = [
    'Office Supplies',
    'Travel',
    'Meals',
    'Entertainment',
    'Utilities',
    'Rent',
    'Software',
    'Hardware',
    'Professional Services',
    'Marketing',
    'Other',
  ];

  // Payment methods
  const paymentMethods = [
    'Credit Card',
    'Debit Card',
    'Cash',
    'Check',
    'Bank Transfer',
    'PayPal',
    'Other',
  ];

  // No longer need form states - ExpenseForm handles them internally

  // ✅ RTK Query hooks with enhanced query parameters
  const {
    data,
    isLoading,
    error: fetchError,
    isFetching
  } = useGetExpensesQuery({
    ...queryParams,
    search: state.search || undefined
  });

  const { data: vendorsResponse } = useGetVendorsQuery();
  const vendors = vendorsResponse?.data || [];
  // Mutations no longer needed here - ExpenseForm handles them internally
  const [deleteExpense, { isLoading: isDeleting }] = useDeleteExpenseMutation();

  // ✅ Derived state from API response
  const expenses = data?.data || [];
  const pagination = data?.pagination;
  const error = fetchError ? (typeof fetchError === 'string' ? fetchError : 'Failed to fetch expenses') : null;

  // Form handlers no longer needed - ExpenseForm handles them internally

  // Handle edit button click - simplified since ExpenseForm handles the form state
  const handleEditClick = (expense: Expense) => {
    setSelectedExpense(expense);
    setIsEditDialogOpen(true);
  };

  // Handle delete button click
  const handleDeleteClick = (expense: Expense) => {
    setSelectedExpense(expense);
    setIsDeleteDialogOpen(true);
  };

  // Form submission handlers no longer needed - ExpenseForm handles them internally

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!selectedExpense) {
      return;
    }

    try {
      await deleteExpense(selectedExpense.id).unwrap();

      // Close dialog and reset selection
      setIsDeleteDialogOpen(false);
      setSelectedExpense(null);

      toast({
        title: common('success'),
        description: t('dialog.messages.deleteSuccess'),
      });
    } catch (error: any) {
      console.error("Error deleting expense:", error);

      // Extract error message from API response
      let errorMessage = 'Failed to delete expense.';
      if (error?.data?.error) {
        errorMessage = error.data.error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: common('error'),
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  // Use the formatters hook for consistent formatting
  const { formatCurrency } = useFormatters('USD');

  // We'll use the DateTime component directly in the JSX for consistent formatting

  // ✅ Action handlers for columns
  const handleEdit = (expense: any) => {
    handleEditClick(expense);
  };

  const handleDelete = (expense: any) => {
    handleDeleteClick(expense);
  };

  // ✅ Integration handlers
  const handleManageIntegrations = (expense: Expense) => {
    setSelectedExpenseForIntegration(expense);
    setIsItemIntegrationDialogOpen(true);
  };

  // Get vendor name by ID
  const getVendorName = (vendorId: string) => {
    if (!vendors) return 'Loading...';
    const vendor = vendors.find((v) => v.id === vendorId);
    return vendor ? vendor.name : 'Unknown Vendor';
  };

  // ✅ Enhanced handlers for bulk operations
  const handleBulkDelete = async (ids: string[]) => {
    try {
      // Filter to only delete pending expenses
      const expensesToDelete = expenses.filter(expense =>
        ids.includes(expense.id) && expense.status === 'PENDING'
      );

      const nonPendingCount = ids.length - expensesToDelete.length;

      if (expensesToDelete.length === 0) {
        toast({
          title: common('error'),
          description: 'No expenses can be deleted. Only pending expenses can be deleted.',
          variant: 'destructive',
        });
        return;
      }

      // Delete only the pending expenses
      await Promise.all(expensesToDelete.map(expense => deleteExpense(expense.id).unwrap()));
      setSelectedIds([]);

      let message = `${expensesToDelete.length} expenses deleted successfully`;
      if (nonPendingCount > 0) {
        message += `. ${nonPendingCount} expenses were skipped (only pending expenses can be deleted)`;
      }

      toast({
        title: common('success'),
        description: message,
      });
    } catch (error: any) {
      console.error('Bulk delete failed:', error);

      // Extract error message from API response
      let errorMessage = 'Failed to delete expenses';
      if (error?.data?.error) {
        errorMessage = error.data.error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: common('error'),
        description: errorMessage,
        variant: 'destructive',
      });
    }
  };

  const handleBulkExport = async (ids: string[]) => {
    setIsExporting(true);
    try {
      const selectedExpenses = expenses.filter(expense => ids.includes(expense.id));
      const csvData = selectedExpenses.map(expense => ({
        Date: expense.date,
        Vendor: expense.vendor_name || getVendorName(expense.vendor_id),
        Category: expense.category,
        Description: expense.description,
        Amount: expense.amount,
        'Payment Method': expense.payment_method,
        Status: expense.status
      }));

      // Convert to CSV and download
      const headers = Object.keys(csvData[0] || {});
      const csvContent = [
        headers.join(','),
        ...csvData.map(row => headers.map(header => row[header]).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `expenses-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: common('success'),
        description: `${ids.length} expenses exported successfully`,
      });
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: common('error'),
        description: 'Failed to export expenses',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  // ✅ Create bulk actions
  const bulkActions = createCommonBulkActions(t, {
    onBulkDelete: handleBulkDelete,
    onBulkExport: handleBulkExport,
  });

  // ✅ Advanced filters configuration
  const filters = [
    {
      key: 'status',
      type: 'select' as const,
      label: 'Status',
      options: [
        { value: 'all', label: 'All Statuses' },
        { value: 'PENDING', label: 'Pending' },
        { value: 'APPROVED', label: 'Approved' },
        { value: 'REJECTED', label: 'Rejected' },
        { value: 'PAID', label: 'Paid' }
      ],
      value: statusFilter,
      onChange: setStatusFilter
    },
    {
      key: 'category',
      type: 'select' as const,
      label: 'Category',
      options: [
        { value: 'all', label: 'All Categories' },
        ...expenseCategories.map(cat => ({ value: cat, label: cat }))
      ],
      value: categoryFilter,
      onChange: setCategoryFilter
    },
    {
      key: 'dateRange',
      type: 'dateRange' as const,
      label: 'Date Range',
      value: dateRange,
      onChange: setDateRange
    },
    {
      key: 'amountRange',
      type: 'numberRange' as const,
      label: 'Amount Range',
      value: amountRange,
      onChange: setAmountRange,
      min: 0,
      step: 0.01,
      placeholder: { min: 'Min amount', max: 'Max amount' }
    }
  ];

  // ✅ Get column definitions
  const columns = useExpenseColumns(
    handleEdit,
    handleDelete,
    getVendorName,
    true, // canEdit
    true, // canDelete
    handleManageIntegrations // ✅ Pass integration handler
  );

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">{t('title')}</h1>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsIntegrationDialogOpen(true)}
          >
            <Settings className="mr-2 h-4 w-4" />
            Integration Settings
          </Button>
          <Button onClick={() => setIsAddDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            {t('addNew')}
          </Button>
        </div>
      </div>

      {/* ✅ Bulk Actions Bar */}
      {selectedIds.length > 0 && (
        <BulkActions
          selectedIds={selectedIds}
          totalCount={expenses.length}
          actions={bulkActions}
          onClearSelection={() => setSelectedIds([])}
          isLoading={isExporting}
        />
      )}

      {/* ✅ Enhanced PaginatedTable with all advanced features */}
      <PaginatedTable
        data={expenses}
        columns={columns}
        paginationInfo={pagination}
        isLoading={isLoading}
        error={error}
        isFetching={isFetching}

        // ✅ Selection functionality
        selectable
        selectedIds={selectedIds}
        onSelectionChange={setSelectedIds}
        getRowId={(expense) => expense.id}

        // ✅ Advanced filters - only pass select filters for now
        filters={filters.filter(filter => filter.type === 'select')}

        // Search functionality
        searchable
        searchValue={state.search}
        onSearchChange={actions.handleSearch}
        searchPlaceholder={t('searchPlaceholder')}

        // Sorting
        sortBy={state.sortBy}
        sortOrder={state.sortOrder}
        onSortChange={actions.handleSort}

        // Pagination
        currentPage={state.page}
        pageSize={state.limit}
        onPageChange={actions.setPage}
        onPageSizeChange={actions.setLimit}

        // ✅ Export functionality - handled by bulk actions instead

        // Display
        title={t('table.title')}
        emptyMessage={t('table.noExpensesFound')}
      />

      {/* Add Expense Dialog */}
      {selectedBranchId && (
        <ExpenseFormDialog
          isOpen={isAddDialogOpen}
          onOpenChange={setIsAddDialogOpen}
          branchId={selectedBranchId}
          onSuccess={() => {
            // Refresh the expenses list
            // The ExpenseForm handles the toast notification
          }}
        />
      )}

      {/* Edit Expense Dialog */}
      {selectedBranchId && (
        <ExpenseFormDialog
          isOpen={isEditDialogOpen}
          onOpenChange={(open) => {
            if (!open) {
              setSelectedExpense(null);
            }
            setIsEditDialogOpen(open);
          }}
          expense={selectedExpense || undefined}
          branchId={selectedBranchId}
          onSuccess={() => {
            // Refresh the expenses list
            // The ExpenseForm handles the toast notification
            setSelectedExpense(null);
          }}
        />
      )}

      {/* Delete Expense Dialog */}
      <DeleteExpenseDialog
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        isLoading={isDeleting}
        selectedExpense={selectedExpense}
        onCancel={() => {
          setIsDeleteDialogOpen(false);
          setSelectedExpense(null);
        }}
      />

      {/* ✅ Expense Integration Dialog */}
      <ExpenseIntegrationDialog
        open={isIntegrationDialogOpen}
        onOpenChange={setIsIntegrationDialogOpen}
        branchId={selectedBranchId || undefined}
      />

      {/* ✅ Expense Item Integration Dialog */}
      {selectedExpenseForIntegration && (
        <ExpenseItemIntegrationDialog
          expenseId={selectedExpenseForIntegration.id}
          expenseName={selectedExpenseForIntegration.description || 'Expense'}
          open={isItemIntegrationDialogOpen}
          onOpenChange={setIsItemIntegrationDialogOpen}
          branchId={selectedBranchId || undefined}
        />
      )}
    </div>
  );
}
